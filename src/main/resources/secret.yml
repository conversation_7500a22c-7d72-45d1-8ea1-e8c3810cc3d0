server:
  port: ${PORT}

spring:
  # Kafka
  kafka:
    bootstrap-servers: ${KAFKA_URLS}
    consumer:
      dlvr:
        topic: ${DLVR_ORD_TOPIC}
      ordr:
        topic: ${ORD_DLVR_TOPIC}
        biz-group-id: ${ORD_BIZ_GROUP}
        check-group-id: ${ORD_CHECK_GROUP}
      cdc:
        pymt-rfnm-requ-topic: ${PYMT_RFNM_REQU_TOPIC}
        biz-group-id: ${ORD_BIZ_GROUP}
        schema-registry: ${SCHEMA_REGISTRY_URL}
    template:
      default-topic: ${ORD_DLVR_TOPIC}
    topic:
      dlvr-log: ${LOG_TOPIC}

  # Redis
  data:
    redis:
      host: ${REDIS_HOST}
      port: ${REDIS_PORT}
      timeout: ${REDIS_TIMEOUT}
      username: ${REDIS_USERNAME}
      password: ${REDIS_PASSWORD}
      ssl:
        enabled: false

  # DB
  datasource:
    master:
      jdbc-url: ${MASTER_DATASOURCE_URL}
      driver-class-name: net.sf.log4jdbc.sql.jdbcapi.DriverSpy
      maximum-pool-size: ${DB_POOL_SIZE}
      username: ${MASTER_DATASOURCE_USERNAME}
      password: ${MASTER_DATASOURCE_PASSWORD}
    readonly:
      jdbc-url: ${READONLY_DATASOURCE_URL}
      driver-class-name: net.sf.log4jdbc.sql.jdbcapi.DriverSpy
      maximum-pool-size: ${DB_POOL_SIZE}
      read-only: true
      username: ${READONLY_DATASOURCE_USERNAME}
      password: ${READONLY_DATASOURCE_PASSWORD}

# Resource
resource:
  api:
    fo:
      auth-token: ${FO_AUTH_TOKEN}
      url:
        mbr: ${MBR_API}
        ord: ${ORD_API}
        kips: ${KIPS_API}
    bo:
      key: ${BO_KEY}
      url:
        dadp: ${DADP_API}
        eadp: ${EADP_API}
        evp: ${EVP_API}
        orp: ${ORP_API}
        mep: ${MEP_API}
        adp: ${ADP_API}
