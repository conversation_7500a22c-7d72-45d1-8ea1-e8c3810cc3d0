<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="kyobobook.application.adapter.out.persistence.mapper.example.ExampleMapper">
    <select id="selectExampleByOrdrId" parameterType="string"
            resultType="kyobobook.application.adapter.out.persistence.entity.mybatis.ExampleResultEntity">
        select /* ExampleMapper.selectExampleByOrdrId, 주문 정보 조회*/
            ordr.ordr_id,        -- 주문ID
            ordr.ordr_srmb,      -- 주문순번
            ordr.ordr_kind_code, -- 주문종류코드,
            ordr.amnr_id         -- 수정자ID
        from eos.tm_ordr ordr -- [1단계표준]SO_주문
        where ordr_id = #{ordrId}
    </select>

    <update id="updateExample"
            parameterType="kyobobook.application.adapter.out.persistence.entity.mybatis.ExampleResultEntity">
        update eos.tm_ordr
        set amnr_id = #{amnrId}
        where ordr_id = #{ordrId}
    </update>
</mapper>
