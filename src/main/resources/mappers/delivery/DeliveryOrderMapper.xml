<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="kyobobook.application.adapter.out.persistence.mapper.deliveryOrderInterface.DeliveryOrderMapper">
    <select id="getOrdrPrgsCdtnCode" parameterType="kyobobook.application.adapter.out.persistence.entity.mybatis.SrchEntity"
            resultType="String">

        SELECT /* DeliveryOrderMapper.getOrdrPrgsCdtnCode 주문진행상태 코드 조회 */
             --  ordr.ordr_id
             --, ordr.onln_ordr_dvsn_code
             --, cmdt.dlvr_rspb_code
             --, cmdt.dlvr_shp_code
             --, cmdt.sale_cmdt_dvsn_code
             --, cmdt.ordr_prgs_cdtn_code
              ( CASE WHEN cmdt.dlvr_rspb_code in ('010', '070')        -- 북시티, 테스트발송
                          THEN CASE WHEN cmdt.sale_cmdt_dvsn_code in ('BNT', 'JNT')
                                        THEN CASE WHEN onln_ordr_dvsn_code in ('300','310')
                                                      THEN COALESCE(NULLIF(split_part(proc.rmrk_3,'|',2), ''), split_part(proc.rmrk_3,'|',1))
                                                  ELSE split_part(proc.rmrk_3,'|', 1)
                                            END
                                        ELSE CASE WHEN onln_ordr_dvsn_code in ('300','310')
                                                  THEN COALESCE(NULLIF(split_part(proc.rmrk_2,'|',2), ''), split_part(proc.rmrk_2,'|',1))
                                              ELSE split_part(proc.rmrk_2,'|', 1)
                                            END
                            END
                      WHEN cmdt.dlvr_rspb_code = '020'                  -- 핫트랙스
                           THEN CASE WHEN onln_ordr_dvsn_code in ('300','310')
                                       THEN COALESCE(NULLIF(split_part(proc.rmrk_4,'|',2), ''), split_part(proc.rmrk_4,'|',1))
                                   ELSE split_part(proc.rmrk_4,'|', 1)
                            END
                      WHEN cmdt.dlvr_rspb_code = '030'                  -- 디지털컨텐츠
                          THEN CASE WHEN cmdt.sale_cmdt_dvsn_code = 'SAM'
                                        THEN proc.rmrk_9
                                    ELSE proc.rmrk_10
                            END
                      WHEN cmdt.dlvr_rspb_code = '040'  -- 오픈마켓
                          THEN CASE WHEN onln_ordr_dvsn_code in ('300','310')
                                        THEN COALESCE(NULLIF(split_part(proc.rmrk_5,'|',2), ''), split_part(proc.rmrk_5,'|',1))
                                    ELSE split_part(proc.rmrk_5,'|', 1)
                            END
                      WHEN cmdt.dlvr_rspb_code = '050'
                          THEN CASE WHEN cmdt.dlvr_shp_code = '130'     -- 바로드림
                                        THEN proc.rmrk_6
                                    ELSE CASE WHEN onln_ordr_dvsn_code in ('300','310')
                                                THEN COALESCE(NULLIF(split_part(proc.rmrk_2,'|',2), ''), split_part(proc.rmrk_2,'|',1))
                                              ELSE split_part(proc.rmrk_2,'|', 1)
                                        END
                            END
                      WHEN cmdt.dlvr_rspb_code = '060'
                          THEN CASE WHEN cmdt.sale_cmdt_dvsn_code = 'CUL'  -- 컬쳐상품
                                        THEN proc.rmrk_7
                                    WHEN cmdt.sale_cmdt_dvsn_code = 'GFC'  -- 기프트카드
                                        THEN proc.rmrk_8
                            END
            END
            ) as tgt_ordr_prgs_cdtn_code
             --, proc.rmrk_2  -- 교보문고 배송 주문상태
             --, proc.rmrk_3  -- 교보문고 해외배송 주문상태 (BNT, JNT )
             --, proc.rmrk_4  -- 핫트랙스/업체배송
             --, proc.rmrk_5  -- 업체배송 ( 오픈마켓 )
             --, proc.rmrk_6  -- 바로드림
             --, proc.rmrk_7  -- 컬쳐상품
             --, proc.rmrk_8  -- 상품권 ( 기프트카드 )
             --, proc.rmrk_9  -- SAM 이용권
             --, proc.rmrk_10 -- 디지털컨텐츠 상품
        FROM eos.tm_ordr ordr
        INNER JOIN eos.td_ordr_cmdt cmdt
                ON cmdt.ordr_id = ordr.ordr_id
               AND cmdt.ordr_srmb = ordr.ordr_srmb
        INNER JOIN eos.tm_unfy_cmdt unfy
                ON cmdt.unfy_cmdtid = unfy.unfy_cmdtid
        INNER JOIN eos.t_so_ordr_proc_code_d proc
                ON proc.code_id = '1691'
        WHERE ordr.ordr_id = #{ordrId}
          AND ordr.ordr_srmb = #{ordrSrmb}
          AND cmdt.ordr_cmdt_srmb = #{ordrCmdtSrmb}
          AND cmdt.ordr_cmdt_pros_srmb = #{ordrCmdtProsSrmb}
          AND proc.code_wrth = #{dlvrPrgsCdtnCode}
          AND proc.dlt_ysno = 'N'
    </select>

    <update id="updateOrdrPrgsCdtnCode" parameterType="kyobobook.application.adapter.out.persistence.entity.mybatis.DeliveryOrderContentEntity">
        UPDATE /* DeliveryOrderMapper.updateOrdrPrgsCdtnCode 주문진행상태 업데이트 */
            eos.td_ordr_cmdt
           SET 	ordr_prgs_cdtn_code = #{ordrPrgsCdtnCode}
                <if test="dlvrRspbCode != null and dlvrRspbCode != ''">
                    , dlvr_rspb_code = #{dlvrRspbCode}
                </if>
                <if test="dlvrShpCode != null and dlvrShpCode != ''">
                    , dlvr_shp_code = #{dlvrShpCode}
                </if>
              , amnr_id = #{amnrId}
              , amnd_dttm = now()
        WHERE ordr_id = #{ordrId}
          AND ordr_srmb = #{ordrSrmb}
          AND ordr_cmdt_srmb = #{ordrCmdtSrmb}
          AND ordr_cmdt_pros_srmb = #{ordrCmdtProsSrmb}
    </update>

    <insert id="insertOrdrCmdtPrgsHist" parameterType="kyobobook.application.adapter.out.persistence.entity.mybatis.DeliveryOrderContentEntity">

        WITH UPSERT AS ( /* DeliveryOrderMapper.insert 주문진행상태 업데이트 */
            UPDATE eos.th_ordr_cmdt_mdfc -- SO_주문상품변경 중복체크
                SET amnr_id = #{amnrId} -- 수정자ID
                    , amnd_dttm = NOW() -- 수정일시
                WHERE ordr_id = #{ordrId}
                    AND ordr_srmb = #{ordrSrmb}
                    AND ordr_cmdt_srmb = #{ordrCmdtSrmb}
                    AND ordr_cmdt_pros_srmb = #{ordrCmdtProsSrmb}
                    AND ordr_cmdt_mdfc_cntt_dvsn_code = '001' -- 주문상품변경내용구분코드 ( 001 : 주문진행상태변경 )
                    AND ordr_prgs_cdtn_code = #{ordrPrgsCdtnCode}
                    AND ordr_cmdt_mdfc_srmb = (SELECT MAX(ordr_cmdt_mdfc_srmb)
                                               FROM eos.th_ordr_cmdt_mdfc
                                               WHERE ordr_id = #{ordrId}
                                                 AND ordr_srmb = #{ordrSrmb}
                                                 AND ordr_cmdt_srmb = #{ordrCmdtSrmb}
                                                 AND ordr_cmdt_pros_srmb = #{ordrCmdtProsSrmb}
                                                 AND dlt_ysno = 'N')
                RETURNING *)
        INSERT
        INTO eos.th_ordr_cmdt_mdfc( ordr_id
                                  , ordr_srmb
                                  , ordr_cmdt_srmb
                                  , ordr_cmdt_pros_srmb
                                  , ordr_cmdt_mdfc_srmb
                                  , ordr_cmdt_mdfc_cntt_dvsn_code
                                  , ordr_prgs_cdtn_code
                                  , ordr_memo_id
                                  , ordr_dlpn_id
                                  , ordr_memo_mdfc_srmb
                                  , dlvr_requ_id
                                  , dlvr_mdfc_srmb
                                  , crtr_id
                                  , cret_dttm
                                  , amnr_id
                                  , amnd_dttm)
        SELECT ordr_id
             , ordr_srmb
             , ordr_cmdt_srmb
             , ordr_cmdt_pros_srmb
             , (SELECT coalesce(MAX(ordr_cmdt_mdfc_srmb), 0) + 1
                FROM eos.th_ordr_cmdt_mdfc
                WHERE ordr_id = #{ordrId}
                  AND ordr_srmb = #{ordrSrmb}
                  AND ordr_cmdt_srmb = #{ordrCmdtSrmb}
                  AND ordr_cmdt_pros_srmb = #{ordrCmdtProsSrmb}) AS ordr_cmdt_mdfc_srmb
             , '001'                                             AS mdfc_dvsn_code -- 주문상품변경내용구분코드 ( 001 : 주문진행상태변경 )
             , #{ordrPrgsCdtnCode}                               AS ordr_prgs_cdtn_code
             , ''                                                AS ordr_memo_id
             , ordr_dlpn_id
             , null                                              AS ordr_memo_mdfc_srmb
             , #{dlvrRequId}                                     as dlvr_requ_id
             , (SELECT coalesce(MAX(dlvr_mdfc_srmb), 0) + 1
                FROM eos.th_ordr_cmdt_mdfc
                WHERE ordr_id = #{ordrId}
                  AND ordr_srmb = #{ordrSrmb}
                  AND ordr_cmdt_srmb = #{ordrCmdtSrmb}
                  AND ordr_cmdt_pros_srmb = #{ordrCmdtProsSrmb}) AS dlvr_mdfc_srmb
             , crtr_id                                           as crtr_id
             , now()                                             AS cret_dttm
             , amnr_id                                           as amnr_id
             , now()                                             AS amnd_dttm
        FROM eos.td_ordr_cmdt
        WHERE ordr_id = #{ordrId}
          AND ordr_srmb = #{ordrSrmb}
          AND ordr_cmdt_srmb = #{ordrCmdtSrmb}
          AND ordr_cmdt_pros_srmb = #{ordrCmdtProsSrmb}
          AND dlt_ysno = 'N'
          AND NOT EXISTS(SELECT * FROM UPSERT)
    </insert>
</mapper>
