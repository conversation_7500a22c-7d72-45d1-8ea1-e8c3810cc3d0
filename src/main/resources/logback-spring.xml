<?xml version="1.0" encoding="UTF-8"?>
<configuration debug="true">
    <!-- property :: LOG_PREFIX  -->
    <springProperty scope="context" name="LOG_PREFIX" source="spring.application.name"/>

    <!-- Console log Appender -->
    <appender name="Console" class="ch.qos.logback.core.ConsoleAppender">
        <encoder class="ch.qos.logback.classic.encoder.PatternLayoutEncoder">
            <Pattern>
                [${LOG_PREFIX}] %d{yyyy-MM-dd HH:mm:ss} [%thread] [%level] %logger{36} - %msg%n
            </Pattern>
            <charset>UTF-8</charset>
        </encoder>
    </appender>

    <logger name="kyobobook" level="INFO"/>

    <!-- 필요시 특정 클래스 로그 레벨을 조정하기 위해서 주석처리 -->

    <!--    <logger name="com.zaxxer.hikari" level="INFO"/>-->
    <!--    <logger name="org.springframework.beans" level="INFO" />-->
    <!--    <logger name="org.springframework.web" level="INFO" />-->
    <!--    <logger name="org.springframework.jdbc" level="INFO" />-->
    <!--    <logger name="org.springframework.data.redis.core" level="INFO" />-->
    <!--    <logger name="jdbc.resultsettable" level="INFO"/>-->
    <!--    <logger name="org.mybatis.spring" level="INFO" />-->
    <!--    <logger name="_org.springframework" level="INFO" />-->
    <!--    <logger name="org.hibernate.validator" level="INFO" />-->
    <logger name="org.hibernate.SQL" level="DEBUG" />
    <!--    <logger name="io.lettuce.core" level="INFO" />-->
    <!--    <logger name="io.netty.util" level="INFO" />-->
    <logger name="org.springframework.kafka" level="DEBUG" />
    <logger name="jdbc" level="OFF"/>
    <!--    <logger name="jdbc.audit" level="OFF"/>-->
    <!--    <logger name="jdbc.resultset" level="OFF"/>-->
    <!--    <logger name="jdbc.connection" level="OFF"/>-->
    <!--    <logger name="jdbc.sqltiming" level="OFF"/>-->
    <logger name="log4jdbc" level="OFF"/>
    <!--    <logger name="log4jdbc.log4j2" level="OFF"/>-->
    <!--    <logger name="log4jdbc.debug" level="OFF"/>-->


    <root level="INFO">
        <appender-ref ref="Console"/>
    </root>


</configuration>
