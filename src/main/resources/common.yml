server:
  shutdown: graceful

management:
  health:
    redis:
      enabled: false
  endpoints:
    web:
      exposure:
        include: health, prometheus
  endpoint:
    health:
      probes:
        enabled: true

spring:
  jpa:
    open-in-view: false
    show-sql: true
    hibernate:
      ddl-auto: none
  kafka:
    consumer:
      auto-offset-reset: earliest
      max-poll-records: 100
      auto-commit-interval: 5000
      heartbeat-interval: 3000
      fetch-max-wait: 500
      enable-auto-commit: false
