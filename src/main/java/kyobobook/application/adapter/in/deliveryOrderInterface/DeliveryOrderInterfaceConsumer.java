package kyobobook.application.adapter.in.deliveryOrderInterface;

import kyobobook.application.adapter.out.external.client.alert.AlertClient;
import kyobobook.application.adapter.out.external.payload.alert.AlertMessage;
import kyobobook.application.domain.event.ErrorEventMessage;
import kyobobook.common.enumeration.DeliveryOrderEvntTypeDvsnCode;
import kyobobook.common.enumeration.UnfyOrdrChnlCode;
import kyobobook.common.exception.BizRuntimeException;
import kyobobook.common.utils.MapperUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.messaging.handler.annotation.Payload;
import org.springframework.messaging.handler.annotation.SendTo;
import org.springframework.stereotype.Component;

import java.util.LinkedHashMap;

/**
 * <AUTHOR> bsjeong7
 * @FileName : DeliveryOrderInterfaceConsumer
 * @Date : 2024. 04. 17.
 * @description : 배송정보 변경 토픽 수신 처리
 */
@Component
@RequiredArgsConstructor
@Slf4j
public class DeliveryOrderInterfaceConsumer {

    private final DeliveryOrderInterfaceController deliveryOrderInterfaceController;
    private final MapperUtils mapperUtils;
    private final AlertClient alertClient;

    @KafkaListener(
            topics = {"${spring.kafka.consumer.dlvr.topic}"},
            groupId = "${spring.kafka.consumer.ordr.biz-group-id}",
            containerFactory = "dlvrOrdrListener",
            id = "${spring.kafka.consumer.ordr.biz-group-id}",
            errorHandler = "ordDlvrErrorHandler"
    )
    @SendTo("${spring.kafka.topic.dlvr-log}")
    public void deliveryOrderTopicListener(ConsumerRecord<String, String> consumerRecord, @Payload String message) {
        log.info(consumerRecord.toString());

        var eventMessage = this.mapperUtils.convertToEventMessage(message);

        // 통합 주문이 아닌 경우 처리하지 않는다.
        if (!UnfyOrdrChnlCode.UNIFY_ORDER.match(eventMessage.unfyOrdrChnlCode())) {
            return;
        }

        deliveryOrderInterfaceController.handleMessage(eventMessage);
    }

    @KafkaListener(
            topics = {"${spring.kafka.topic.dlvr-log}"},
            groupId = "${spring.kafka.consumer.ordr.check-group-id}",
            containerFactory = "ordrErrorListener",
            id = "error-listener"
    )
    public void ordrDeliveryErrorEventConsume(ConsumerRecord<String, String> consumerRecord, @Payload String message) {
        log.debug(consumerRecord.toString());

        // Error Event Message 컨버팅
        ErrorEventMessage errorEventMessage;
        // 배송-주문 이벤트 타입 구분 코드명 처리 (에러 메세지 웹훅 처리를 위해서)
        DeliveryOrderEvntTypeDvsnCode evntTypeDvsnCode;
        try {
            errorEventMessage = this.mapperUtils.convertToErrorEventMessage(message);
            evntTypeDvsnCode = errorEventMessage.kafkaMessage().getDeliveryOrderEvntTypeDvsnCode();
        } catch (BizRuntimeException e) {
            // 주문-배송 컨슈머에서 처리하지 않는 이벤트 타입 구분 코드인 경우 이후 로직 처리 X
            return;
        }

        // 통합 주문이 아닌 에러 메세지인 경우 처리하지 않는다.
        if (!UnfyOrdrChnlCode.UNIFY_ORDER.match(errorEventMessage.kafkaMessage().unfyOrdrChnlCode())) {
            return;
        }

        // Teams Web Hook 처리
        var attributes = new LinkedHashMap<String, String>();
        attributes.put("토픽명", consumerRecord.topic());
        attributes.put("처리 내용", "<b>메세지 컨슈밍 중 예외가 발생하여 재시도를 시작합니다.</b>");
        attributes.put("에러 내용", errorEventMessage.errorMessage());
        attributes.put("이벤트 메세지", AlertMessage.convertHtml(errorEventMessage.kafkaMessage().toAttributes()));
        attributes.put("Stack Trace", errorEventMessage.stackTrace());

        var alertMessage = AlertMessage.newInstance(evntTypeDvsnCode, attributes);
        alertClient.errorAlert(alertMessage);

        // Error Handler 처리
        deliveryOrderInterfaceController.errorHandleMessage(errorEventMessage);
    }
}
