package kyobobook.application.adapter.in.deliveryOrderInterface;

import kyobobook.application.biz.DeliveryOrderInterface.port.in.DeliveryOrderInterfacePort;
import kyobobook.application.domain.event.ErrorEventMessage;
import kyobobook.application.domain.event.EventMessage;
import kyobobook.common.enumeration.DeliveryOrderEvntTypeDvsnCode;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.EnumMap;
import java.util.List;
import java.util.Map;

@Component
@Slf4j
public class DeliveryOrderInterfaceController {

    private final Map<DeliveryOrderEvntTypeDvsnCode, DeliveryOrderInterfacePort> deliveryOrderInterfacePorts;

    public DeliveryOrderInterfaceController(List<DeliveryOrderInterfacePort> ports) {
        this.deliveryOrderInterfacePorts = new EnumMap<>(DeliveryOrderEvntTypeDvsnCode.class);
        ports.forEach(port -> this.deliveryOrderInterfacePorts.put(port.getEventType(), port));
    }

    public void handleMessage(final EventMessage eventMessage) {
        var eventType = eventMessage.getDeliveryOrderEvntTypeDvsnCode();
        deliveryOrderInterfacePorts.get(eventType).handle(eventMessage);
    }

    public void errorHandleMessage(final ErrorEventMessage errorEventMessage) {
        var eventType = errorEventMessage.kafkaMessage().getDeliveryOrderEvntTypeDvsnCode();
        deliveryOrderInterfacePorts.get(eventType).errorHandle(errorEventMessage);
    }
}
