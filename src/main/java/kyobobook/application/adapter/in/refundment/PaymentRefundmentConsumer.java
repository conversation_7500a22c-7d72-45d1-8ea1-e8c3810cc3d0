/***************************************************
 * Copyright(c) 2021-2022 Kyobo Book Centre All right reserved.
 * This software is the proprietary information of Kyobo Book.
 *
 * Revision History
 * Author                         Date          Description
 * --------------------------     ----------    ----------------------------------------
 * <EMAIL>     2025-07-08
 *
 ****************************************************/
package kyobobook.application.adapter.in.refundment;

import kyobobook.application.adapter.out.external.client.alert.AlertClient;
import kyobobook.application.domain.event.PymtRfmnLnkgEvent;
import kyobobook.application.domain.event.PymtRfmnLnkgEventKey;
import kyobobook.common.utils.MapperUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.avro.generic.GenericRecord;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.apache.kafka.common.header.Headers;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.kafka.support.KafkaHeaders;
import org.springframework.stereotype.Component;

import java.nio.charset.StandardCharsets;

/**
 * <AUTHOR> <EMAIL>
 * @Project : cm-msg-ord-dlvr
 * @FileName : PaymentRefundmentConsumer
 * @Date : 2025-07-08
 * @description :
 */

@Component
@RequiredArgsConstructor
@Slf4j
public class PaymentRefundmentConsumer {

    private final MapperUtils mapperUtils;
    private final AlertClient alertClient;
    private final PaymentRefundmentFacade paymentRefundmentController;



    /**
     * @method : paymentRefundmentConsume
     * <AUTHOR> <EMAIL>
     * @description : eos.t_so_pymt_rfmn_lnkg_r CDC consuming
     */
    @KafkaListener(
            topics = {"${spring.kafka.consumer.cdc.pymt-rfnm-requ-topic}"},
            groupId = "${spring.kafka.consumer.cdc.biz-group-id}",
            containerFactory = "paymentRefundListener",
            id = "pymt_rfmn_lnkg-listener"
    )
    public void paymentRefundmentConsume(ConsumerRecord<Object, GenericRecord> consumerRecord) {

        final PymtRfmnLnkgEventKey pymtRfmnLnkgEventKey = mapperUtils.convertRecord(consumerRecord.key().toString(), PymtRfmnLnkgEventKey.class);
        final PymtRfmnLnkgEvent pymtRfmnLnkgEvent = mapperUtils.convertRecord(consumerRecord.value().toString(), PymtRfmnLnkgEvent.class);

        if (!pymtRfmnLnkgEvent.isCreateOperation()) {
            return; // 우선 생성만
        }

        paymentRefundmentController.handleMessage(pymtRfmnLnkgEvent);
    }

    /**
     * @method : paymentRefundmentErrorConsume
     * <AUTHOR> <EMAIL>
     * @description : 에러 핸들링 로직
     */
    @KafkaListener(
            topics = {"${spring.kafka.topic.pymt-rfnm-requ-topic}" + "-dlt"},
            groupId = "${spring.kafka.consumer.cdc.biz-group-id}",
            containerFactory = "rfmnLnkgCdcErrorListener",
            id = "rfmn-lnkg-cdc-error-listener"
    )
    public void paymentRefundmentErrorConsume(ConsumerRecord<Object, GenericRecord> consumerRecord) {

        Headers headers = consumerRecord.headers();
        String topic =  new String(headers.lastHeader(KafkaHeaders.DLT_ORIGINAL_TOPIC).value(), StandardCharsets.UTF_8);
        String exception = new String(headers.lastHeader(KafkaHeaders.DLT_EXCEPTION_MESSAGE).value(), StandardCharsets.UTF_8);
        String stackTrace =  new String(headers.lastHeader(KafkaHeaders.DLT_EXCEPTION_STACKTRACE).value(), StandardCharsets.UTF_8);

        paymentRefundmentController.errorHandleMessage(consumerRecord);

    }
}
