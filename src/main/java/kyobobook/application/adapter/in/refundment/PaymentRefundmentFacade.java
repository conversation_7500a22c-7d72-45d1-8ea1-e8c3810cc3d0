/***************************************************
 * Copyright(c) 2021-2022 Kyobo Book Centre All right reserved.
 * This software is the proprietary information of Kyobo Book.
 *
 * Revision History
 * Author                         Date          Description
 * --------------------------     ----------    ----------------------------------------
 * <EMAIL>     2025-07-10
 *
 ****************************************************/
package kyobobook.application.adapter.in.refundment;

import kyobobook.application.adapter.out.persistence.repository.PaymentDetailRepository;
import kyobobook.application.biz.settlement.port.in.RefundmentPort;
import kyobobook.application.domain.event.PymtRfmnLnkgEvent;
import kyobobook.common.enumeration.StlmMthdCode;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.EnumMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR> <EMAIL>
 * @Project : cm-msg-ord-dlvr
 * @FileName : PaymentRefundmentController
 * @Date : 2025-07-10
 * @description :
 */
@Component
@Slf4j
public class PaymentRefundmentFacade {

    private final Map<StlmMthdCode, RefundmentPort> refundmentPorts;
    private final PaymentDetailRepository  paymentDetailRepository;

    public PaymentRefundmentFacade(List<RefundmentPort> ports) {
        this.refundmentPorts = new EnumMap<>(StlmMthdCode.class);
        ports.forEach(port -> this.refundmentPorts.put(port.getEventType(), port));
    }

    public void handleMessage(final PymtRfmnLnkgEvent pymtRfmnLnkgEvent) {
        // TODO: ordrStlmId를 통해 StlmMthdCode를 조회하는 로직 필요
        // StlmMthdCode stlmMthdCode = getStlmMthdCodeByOrdrStlmId(pymtRfmnLnkgEvent.getOrdrStlmId());
        //refundmentPorts.get(stlmMthdCode).handle(pymtRfmnLnkgEvent);

    }

    public void errorHandleMessage(final Object errorObject) {
        // TODO: 에러 처리 로직 구현
        log.error("Payment refundment error occurred: {}", errorObject);
    }
}
