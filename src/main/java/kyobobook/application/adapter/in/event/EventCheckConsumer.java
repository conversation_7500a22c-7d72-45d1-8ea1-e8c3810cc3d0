/***************************************************
 * Copyright(c) 2021-2022 Kyobo Book Centre All right reserved.
 * This software is the proprietary information of Kyobo Book.
 *
 * Revision History
 * Author                         Date          Description
 * --------------------------     ----------    ----------------------------------------
 * SeongHeon Sim     2024-06-11
 *
 ****************************************************/
package kyobobook.application.adapter.in.event;

import kyobobook.application.biz.event.port.in.EventCheckPort;
import kyobobook.common.utils.MapperUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.messaging.handler.annotation.Payload;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR> SeongHeon Sim
 * @Project : ord
 * @FileName : EventCheckerConsumer
 * @Date : 2024-06-11
 * @description :
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class EventCheckConsumer {

    private final EventCheckPort eventCheckPort;

    private final MapperUtils mapperUtils;

    @KafkaListener(
            topics = {"${spring.kafka.consumer.ordr.topic}"},
            groupId = "${spring.kafka.consumer.ordr.check-group-id}",
            containerFactory = "ordrCheckListener",
            id = "${spring.kafka.consumer.ordr.check-group-id}"
    )
    public void checkOrderEvent(ConsumerRecord<String, String> consumerRecord, @Payload String message) {
        log.debug(consumerRecord.toString());

        // 카프카 내 메시지를 역직렬화
        var eventMessage = mapperUtils.convertToEventMessage(message);
        log.debug(eventMessage.toString());

        // event 테이블에 발행여부를 확인
        eventCheckPort.checkIssue(eventMessage);
    }
}
