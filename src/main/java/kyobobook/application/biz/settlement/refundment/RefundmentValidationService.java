/***************************************************
 * Copyright(c) 2021-2022 Kyobo Book Centre All right reserved.
 * This software is the proprietary information of Kyobo Book.
 *
 * Revision History
 * Author                         Date          Description
 * --------------------------     ----------    ----------------------------------------
 * <EMAIL>     2025-07-10
 *
 ****************************************************/
package kyobobook.application.biz.settlement.refundment;

import kyobobook.application.domain.event.PymtRfmnLnkgEvent;
import kyobobook.application.domain.validation.RefundmentValidator;
import kyobobook.common.enumeration.StlmMthdCode;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR> <EMAIL>
 * @Project : cm-msg-ord-dlvr
 * @FileName : RefundmentValidationService
 * @Date : 2025-07-10
 * @description : 환불 검증 서비스 - Validator 사용 예제
 */
@Service
@Slf4j
public class RefundmentValidationService {

    /**
     * 성공 케이스 검증
     */
    public RefundmentValidator.ValidationResult validateSuccessCase(PymtRfmnLnkgEvent event) {
        RefundmentValidator validator = RefundmentValidator.builder()
                .withCommonValidations()
                .withSuccessValidations()
                .addRule(event -> event.getCretDttm() != null,
                        "생성일시는 필수입니다")
                .build();

        return validator.validate(event);
    }

    /**
     * 실패 케이스 검증
     */
    public RefundmentValidator.ValidationResult validateFailureCase(PymtRfmnLnkgEvent event) {
        RefundmentValidator validator = RefundmentValidator.builder()
                .withCommonValidations()
                .withFailureValidations()
                .addRule(event -> event.getFlrRsn().length() <= 500,
                        "실패사유는 500자를 초과할 수 없습니다")
                .build();

        return validator.validate(event);
    }

    /**
     * 결제수단별 검증
     */
    public RefundmentValidator.ValidationResult validateByPaymentMethod(PymtRfmnLnkgEvent event, StlmMthdCode stlmMthdCode) {
        RefundmentValidator.Builder builder = RefundmentValidator.builder()
                .withCommonValidations();

        // 결제수단별 특별 검증 로직
        switch (stlmMthdCode) {
            case 신용카드:
                builder.addRule(event -> event.getOrdrStlmId().startsWith("CC"),
                        "신용카드 결제ID는 CC로 시작해야 합니다");
                break;
            case 카카오페이:
                builder.addRule(event -> event.getOrdrStlmId().startsWith("KP"),
                        "카카오페이 결제ID는 KP로 시작해야 합니다");
                break;
            case 네이버페이:
                builder.addRule(event -> event.getOrdrStlmId().startsWith("NP"),
                        "네이버페이 결제ID는 NP로 시작해야 합니다");
                break;
            default:
                // 기본 검증
                break;
        }

        return builder.build().validate(event);
    }

    /**
     * 복합 조건 검증 예제
     */
    public RefundmentValidator.ValidationResult validateComplexConditions(PymtRfmnLnkgEvent event) {
        RefundmentValidator validator = RefundmentValidator.builder()
                .withCommonValidations()
                // 조건부 검증: 성공인 경우에만 생성자ID 체크
                .addConditionalRule(
                        event -> "Y".equals(event.getSuccsYsno()),
                        event -> event.getCrtrId() != null && !event.getCrtrId().trim().isEmpty(),
                        "성공 케이스에서는 생성자ID가 필수입니다"
                )
                // AND 조건: 성공이면서 생성일시가 있어야 함
                .addAndRule(
                        event -> "Y".equals(event.getSuccsYsno()),
                        event -> event.getCretDttm() != null,
                        "성공 케이스에서는 생성일시가 필수입니다"
                )
                // OR 조건: 생성자ID 또는 수정자ID 중 하나는 있어야 함
                .addOrRule(
                        event -> event.getCrtrId() != null && !event.getCrtrId().trim().isEmpty(),
                        event -> event.getAmnrId() != null && !event.getAmnrId().trim().isEmpty(),
                        "생성자ID 또는 수정자ID 중 하나는 필수입니다"
                )
                .build();

        return validator.validate(event);
    }

    /**
     * 비즈니스 로직 검증 예제
     */
    public RefundmentValidator.ValidationResult validateBusinessRules(PymtRfmnLnkgEvent event) {
        RefundmentValidator validator = RefundmentValidator.builder()
                .withCommonValidations()
                // 커스텀 비즈니스 로직
                .addRule(event -> {
                    // 예: 주말에는 환불 처리 불가
                    // LocalDateTime now = LocalDateTime.now();
                    // DayOfWeek dayOfWeek = now.getDayOfWeek();
                    // return dayOfWeek != DayOfWeek.SATURDAY && dayOfWeek != DayOfWeek.SUNDAY;
                    return true; // 임시로 항상 true
                }, "주말에는 환불 처리가 불가능합니다")
                .addRule(event -> {
                    // 예: 특정 시간대에만 처리 가능
                    // LocalTime now = LocalTime.now();
                    // return now.isAfter(LocalTime.of(9, 0)) && now.isBefore(LocalTime.of(18, 0));
                    return true; // 임시로 항상 true
                }, "업무시간(09:00-18:00)에만 환불 처리가 가능합니다")
                .build();

        return validator.validate(event);
    }

    /**
     * 전체 검증 실행
     */
    public boolean validateRefundmentEvent(PymtRfmnLnkgEvent event, StlmMthdCode stlmMthdCode) {
        // 기본 검증
        RefundmentValidator.ValidationResult basicResult = validateSuccessCase(event);
        if (!basicResult.isValid()) {
            log.error("Basic validation failed: {}", basicResult.getErrorMessage());
            return false;
        }

        // 결제수단별 검증
        RefundmentValidator.ValidationResult paymentResult = validateByPaymentMethod(event, stlmMthdCode);
        if (!paymentResult.isValid()) {
            log.error("Payment method validation failed: {}", paymentResult.getErrorMessage());
            return false;
        }

        // 비즈니스 규칙 검증
        RefundmentValidator.ValidationResult businessResult = validateBusinessRules(event);
        if (!businessResult.isValid()) {
            log.error("Business rules validation failed: {}", businessResult.getErrorMessage());
            return false;
        }

        log.info("All validations passed for event: {}", event.getOrdrStlmId());
        return true;
    }
}
