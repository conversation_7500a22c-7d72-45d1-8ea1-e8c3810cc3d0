/***************************************************
 * Copyright(c) 2021-2022 Kyobo Book Centre All right reserved.
 * This software is the proprietary information of Kyobo Book.
 *
 * Revision History
 * Author                         Date          Description
 * --------------------------     ----------    ----------------------------------------
 * bsjeong7     2024-04-17
 *
 ****************************************************/
package kyobobook.application.biz.DeliveryOrderInterface.service;

import kyobobook.application.adapter.out.external.client.kips.KipsGiftCardClient;
import kyobobook.application.adapter.out.external.payload.kips.in.KipsSaleGiftCardReq;
import kyobobook.application.adapter.out.persistence.entity.jpa.OrderCommodityId;
import kyobobook.application.biz.DeliveryOrderInterface.attribute.DeliveryOrderConditionRequest;
import kyobobook.application.biz.DeliveryOrderInterface.port.in.progress.DeliveryOrderPort;
import kyobobook.application.biz.DeliveryOrderInterface.port.out.DeliveryOrderPersistencePort;
import kyobobook.application.biz.DeliveryOrderInterface.port.out.OrderCommodityPersistencePort;
import kyobobook.application.biz.DeliveryOrderInterface.port.out.OrderMemoPersistencePort;
import kyobobook.application.biz.DeliveryOrderInterface.port.out.SettlementPersistencePort;
import kyobobook.application.domain.commodity.OrderCommodity;
import kyobobook.application.domain.event.EventMessage;
import kyobobook.application.domain.memo.OrderMemoList;
import kyobobook.application.domain.order.SettlementMaster;
import kyobobook.common.enumeration.DlvrRspbCode;
import kyobobook.common.enumeration.OrdrKindCode;
import kyobobook.common.enumeration.OrdrPrgsCdtnCode;
import kyobobook.common.enumeration.OrdrStlmPatrCode;
import kyobobook.common.enumeration.StlmMthdCode;
import kyobobook.common.exception.BizRuntimeException;
import kyobobook.common.utils.MapperUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.util.List;
import java.util.Objects;
import java.util.stream.LongStream;

/**
 * <AUTHOR> bsjeong7
 * @Project : cm-msg-ord-dlvr
 * @FileName : OrderDeliveryService
 * @Date : 2024-04-17
 * @description :
 */
@Slf4j
@Service
@Transactional
@RequiredArgsConstructor
class DeliveryOrderService implements DeliveryOrderPort {

    private final DeliveryOrderPersistencePort deliveryOrderPersistencePort;
    private final OrderCommodityPersistencePort orderCommodityPersistencePort;
    private final SettlementPersistencePort settlementPersistencePort;
    private final OrderMemoPersistencePort orderMemoPersistencePort;
    private final KipsGiftCardClient kipsGIftCardClient;

    private final MapperUtils mapperUtils;

    @Override
    public void updateOrderProgressCondition(EventMessage eventMessage) {

        DeliveryOrderConditionRequest content = this.mapperUtils.convertRecord(eventMessage.evntCttsCntt(), DeliveryOrderConditionRequest.class);

        // 주문진행상태 코드 조회
        String ordrPrgsCdtnCode = deliveryOrderPersistencePort.getOrdrPrgsCdtnCode(content);

        log.debug("dlvrPrgsCdtnCode : {}, ordrPrgsCdtnCode : {} ", content.dlvrPrgsCdtnCode(), ordrPrgsCdtnCode);

        if (!StringUtils.hasText(ordrPrgsCdtnCode)) {
            throw new BizRuntimeException(101, "매핑대상 없음");
        }

        // 주문진행상태 코드 업데이트 처리
        int resultCnt = deliveryOrderPersistencePort.updateOrdrPrgsCdtnCode(content, ordrPrgsCdtnCode, eventMessage.crtrId());

        if (resultCnt > 0) {
            try {
                // 주문상품 진행상태 업데이트 이력 추가 ( 최종상태가 다른 경우에만 insert )
                deliveryOrderPersistencePort.insertOrdrCmdtPrgsHist(content, ordrPrgsCdtnCode, eventMessage.crtrId());
            } catch (Exception e) {
                // 이력은 저장 실패 되어도 pass
                log.error(e.getMessage(), e.getCause());
            }
        }
    }

    @Override
    public void updateBarodrimDeliveryInfo(String ordrId, Integer ordrSrmb, String ordrDlpnId, String dlvrRequId, String rcptBranCode, String rcptName, String invnBranCode, String addGb, String rqtrId) {
        // 주문 배송지 바로드림 수령처 업데이트
        deliveryOrderPersistencePort.updateBarodrimDeliveryInfo(ordrDlpnId, rcptBranCode, rcptName, invnBranCode, addGb, rqtrId);

        // 바로드림 주문 상품 조회 후 주문 상품 별 배송 요청 ID 업데이트
        orderCommodityPersistencePort.findAll(ordrId, ordrSrmb, DlvrRspbCode.BUSINESS_PLACE)
                .forEach(orderCommodity -> orderCommodityPersistencePort.updateDlvrRequId(orderCommodity.getOrderCommodityId(), dlvrRequId, rqtrId));
    }

    @Override
    public void updateOrdrPrgsCdtnCode(OrderCommodityId orderCommodityId, OrdrPrgsCdtnCode ordrPrgsCdtnCode) {
        orderCommodityPersistencePort.updateOrdrPrgsCdtnCode(orderCommodityId, ordrPrgsCdtnCode, this.getClass().getSimpleName());
    }

    @Override
    public List<OrderCommodity> findAllOrderCommodityIdAndDlvrRspbCodeAndOrdrKindCode(List<OrderCommodityId> orderCommodityIdList, OrdrPrgsCdtnCode ordrPrgsCdtnCode, DlvrRspbCode dlvrRspbCode, List<OrdrKindCode> ordrKindCodeList) {
        return orderCommodityPersistencePort.findAll(orderCommodityIdList, ordrPrgsCdtnCode, dlvrRspbCode, ordrKindCodeList);
    }

    @Override
    public List<SettlementMaster> findAllByOrdrIdAndOrdrStlmPatrCodeAndStlmAndStlmMthdCode(String ordrId, OrdrStlmPatrCode ordrStlmPatrCode, StlmMthdCode stlmMthdCode) {
        return settlementPersistencePort.findAllByOrdrIdAndOrdrStlmPatrCodeAndStlmAndStlmMthdCode(ordrId, ordrStlmPatrCode, stlmMthdCode);
    }

    @Override
    public OrderMemoList findAllByOrdrId(String ordrId) {
        return orderMemoPersistencePort.findAllByOrdrId(ordrId);
    }

    @Override
    public void saleGiftCard(List<OrderCommodity> targetIssueOrderCommodityList, long targetIssueCount, String sendMessage, String imageCode) {
        var totalFailedIssueCount = LongStream.range(0, targetIssueCount)
                .mapToObj(i -> new KipsSaleGiftCardReq(targetIssueOrderCommodityList.get(0), sendMessage, imageCode, this.getClass().getSimpleName()))
                .map(request -> {
                    try {
                        return kipsGIftCardClient.saleGiftCard(request);
                    } catch (Exception e) {
                        return null;
                    }
                })
                .filter(response -> Objects.isNull(response) || response.statusCode().isError())
                .count();

        // 기프트 카드 발급 실패수가 존재하는 경우 예외 발생 > 에러 핸들러에서 나머지 추가 발급 진행하도록 한다.
        if (0 < totalFailedIssueCount) {
            // 에러 메세지
            var errorMessage = "기프트 카드 발급 처리 중 일부가 발급에 실패했습니다.";
            // 에러 로그 출력
            log.error(errorMessage);
            // 예외 발생하여 다시 처리할 수 있도록
            throw new BizRuntimeException(errorMessage);
        }

        // 기프트 카드 발급 처리 및 주문 상품 주문진행상태코드 업데이트
        // 주문 상품 중 수량만큼 발급된 주문 상품만 주문진행상태코드(111 -> 143) 업데이트 및 변경 이력 저장
        targetIssueOrderCommodityList.forEach(orderCommodity -> orderCommodityPersistencePort.updateOrdrPrgsCdtnCode(orderCommodity.getOrderCommodityId(), OrdrPrgsCdtnCode.SENDING_FINISH, this.getClass().getSimpleName()));
    }
}
