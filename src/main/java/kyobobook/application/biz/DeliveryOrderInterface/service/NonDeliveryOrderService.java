/***************************************************
 * Copyright(c) 2021-2022 Kyobo Book Centre All right reserved.
 * This software is the proprietary information of Kyobo Book.
 *
 * Revision History
 * Author                         Date          Description
 * --------------------------     ----------    ----------------------------------------
 * SeongHeon Sim     2025-04-18
 *
 ****************************************************/
package kyobobook.application.biz.DeliveryOrderInterface.service;

import kyobobook.application.adapter.out.external.client.kips.KipsGiftCardClient;
import kyobobook.application.adapter.out.external.payload.common.ResponseMessage;
import kyobobook.application.adapter.out.external.payload.kips.in.KipsSaleGiftCardReq;
import kyobobook.application.adapter.out.external.payload.kips.out.KipsSaleGiftCardRes;
import kyobobook.application.adapter.out.persistence.entity.jpa.OrderCommodityId;
import kyobobook.application.biz.DeliveryOrderInterface.port.in.progress.NonDeliveryOrderPort;
import kyobobook.application.biz.DeliveryOrderInterface.port.out.OrderCommodityPersistencePort;
import kyobobook.application.biz.DeliveryOrderInterface.port.out.OrderMemoPersistencePort;
import kyobobook.application.biz.DeliveryOrderInterface.port.out.SettlementPersistencePort;
import kyobobook.application.domain.commodity.OrderCommodity;
import kyobobook.application.domain.giftcard.GiftCardIssuanceList;
import kyobobook.application.domain.memo.OrderMemoList;
import kyobobook.common.enumeration.DlvrRspbCode;
import kyobobook.common.enumeration.OrdrKindCode;
import kyobobook.common.enumeration.OrdrMemoDvsnCode;
import kyobobook.common.enumeration.OrdrPrgsCdtnCode;
import kyobobook.common.enumeration.OrdrStlmPatrCode;
import kyobobook.common.enumeration.StlmMthdCode;
import kyobobook.common.exception.BizRuntimeException;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * <AUTHOR> SeongHeon Sim
 * @Project : ord
 * @FileName : NonDeliveryOrderService
 * @Date : 2025-04-18
 * @description :
 */
@Slf4j
@Service
@Transactional
@RequiredArgsConstructor
class NonDeliveryOrderService implements NonDeliveryOrderPort {

    private final OrderCommodityPersistencePort orderCommodityPersistencePort;
    private final SettlementPersistencePort settlementPersistencePort;
    private final OrderMemoPersistencePort orderMemoPersistencePort;
    private final KipsGiftCardClient kipsGIftCardClient;

    @Override
    public List<OrderCommodity> findAllIssuableGiftCardOrderCommodityIdList(List<OrderCommodityId> orderCommodityIdList, List<OrdrKindCode> ordrKindCodeList) {
        // 주문 상품 ID 목록이 존재하지 않는다면 빈 리스트 리턴
        if (ObjectUtils.isEmpty(orderCommodityIdList)) {
            return Collections.emptyList();
        }

        // 기프트 카드 주문 상품 조회
        List<OrderCommodity> orderCommodityList = orderCommodityPersistencePort.findAll(orderCommodityIdList, OrdrPrgsCdtnCode.SETTLEMENT_FINISH, DlvrRspbCode.ETC, ordrKindCodeList);

        // 발급 대상 주문 상품 목록이 존재하지 않는다면 빈 리스트 리턴
        if (ObjectUtils.isEmpty(orderCommodityList)) {
            return Collections.emptyList();
        }

        String ordrId = orderCommodityList.get(0).ordrId();
        List<Integer> ordrCmdtSrmbList = orderCommodityList.stream().map(OrderCommodity::ordrCmdtSrmb).toList(); // 주문 상품 순번 리스트

        // 기프트 카드 발급 내역 조회
        Optional<GiftCardIssuanceList> optionalIssuanceList = settlementPersistencePort.findGiftCardIssuanceByOrdrIdAndOrdrCmdtSrmbList(ordrId, ordrCmdtSrmbList);

        // 발급 내역이 존재하지 않는 경우, 모든 주문 상품에 대한 발급 처리를 진행하도록 한다.
        if (optionalIssuanceList.isEmpty()) {
            // 발급 대상으로 조회된 주문 상품만 처리하기 위해서 조회된 주문 상품을 기준으로 주문 상품 ID 리턴
            return orderCommodityList;
        }

        // 발급 내역 중 주문 상품 순번 추출
        GiftCardIssuanceList issuanceList = optionalIssuanceList.get();
        List<Integer> issuedOrdrCmdtSrmbList = issuanceList.getIssuedOrderCommodityId().stream().map(OrderCommodityId::getOrdrCmdtSrmb).distinct().toList();

        // 발급된 주문 상품 순번 제외한 나머지 주문 상품에 한해서 발급 처리 진행하도록 한다.
        return orderCommodityList.stream()
                .filter(ordrCmdtId -> !issuedOrdrCmdtSrmbList.contains(ordrCmdtId.ordrCmdtSrmb()))
                .collect(Collectors.toList());
    }

    @Override
    public void validateSuccessfulSettlement(String ordrId) {
        long finalStlmBlce = settlementPersistencePort.getFinalSettlementBalance(ordrId, OrdrStlmPatrCode.GIFT카드_구매, List.of(StlmMthdCode.교보문고기프트카드, StlmMthdCode.기업기프트카드, StlmMthdCode.나이스복지카드));

        if (finalStlmBlce == 0) {
            // 에러 메세지
            String errorMessage = "기프트카드 주문의 원결제 수단이 0원입니다.";
            // 에러 로그 출력
            log.error("[{}] {} ordrId = {}", this.getClass().getSimpleName(), errorMessage, ordrId);
            // 예외 발생하여 다시 처리할 수 있도록
            throw new BizRuntimeException(errorMessage);
        }
    }

    @Override
    public void saleGiftCard(List<OrderCommodity> issuableOrderCommodityList) {
        // 1. 주문 메모 리스트 조회 (선물 메세지 및 이미지 코드)
        String ordrId = issuableOrderCommodityList.get(0).ordrId();
        OrderMemoList orderMemoList = orderMemoPersistencePort.findAllByOrdrId(ordrId); // 주문 메모 리스트
        String sendMessage = orderMemoList.getOrdrMemoOrNull(OrdrMemoDvsnCode.GIFT_MESSAGE); // 선물 메세지
        String imageCode = orderMemoList.getOrdrMemoOrElse(OrdrMemoDvsnCode.GIFT_CARD_IMAGE, () -> "rep0000001"); // 기프트 카드 이미지 코드

        // 발급 대상 주문 상품 발급 처리
        long totalFailedIssueCount = issuableOrderCommodityList.stream()
                .map(orderCommodity -> {
                    try {
                        KipsSaleGiftCardReq request = new KipsSaleGiftCardReq(orderCommodity, sendMessage, imageCode, this.getClass().getSimpleName());
                        ResponseMessage<KipsSaleGiftCardRes> response = kipsGIftCardClient.saleGiftCard(request);
                        if (response.statusCode().is2xxSuccessful()) {
                            // 주문 상품 중 수량만큼 발급된 주문 상품만 주문진행상태코드(111 -> 143) 업데이트 및 변경 이력 저장
                            orderCommodityPersistencePort.updateOrdrPrgsCdtnCode(orderCommodity.getOrderCommodityId(), OrdrPrgsCdtnCode.SENDING_FINISH, this.getClass().getSimpleName());
                        }
                        return response;
                    } catch (Exception e) {
                        return null;
                    }
                })
                .filter(response -> Objects.isNull(response) || response.statusCode().isError())
                .count();

        // 기프트 카드 발급 실패수가 존재하는 경우 예외 발생 > 에러 핸들러에서 나머지 추가 발급 진행하도록 한다.
        if (0 < totalFailedIssueCount) {
            // 에러 메세지
            String errorMessage = "기프트 카드 발급 처리 중 일부가 발급에 실패했습니다.";
            // 에러 로그 출력
            log.error("[{}] {} ordrId = {}, issuableCount = {}, failCount = {}", this.getClass().getSimpleName(), errorMessage, ordrId, issuableOrderCommodityList.size(), totalFailedIssueCount);
            // 예외 발생하여 다시 처리할 수 있도록
            throw new BizRuntimeException(errorMessage);
        }
    }
}
