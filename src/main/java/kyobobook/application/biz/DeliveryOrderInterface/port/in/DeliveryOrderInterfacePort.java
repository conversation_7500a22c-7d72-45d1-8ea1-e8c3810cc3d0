package kyobobook.application.biz.DeliveryOrderInterface.port.in;

import kyobobook.application.domain.event.ErrorEventMessage;
import kyobobook.application.domain.event.EventMessage;
import kyobobook.common.enumeration.DeliveryOrderEvntTypeDvsnCode;

public interface DeliveryOrderInterfacePort {

    DeliveryOrderEvntTypeDvsnCode getEventType();

    void handle(EventMessage eventMessage);

    default void errorHandle(ErrorEventMessage errorEventMessage) {
    }
}
