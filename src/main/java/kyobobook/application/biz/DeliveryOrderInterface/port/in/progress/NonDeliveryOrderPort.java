/***************************************************
 * Copyright(c) 2021-2022 Kyobo Book Centre All right reserved.
 * This software is the proprietary information of Kyobo Book.
 *
 * Revision History
 * Author                         Date          Description
 * --------------------------     ----------    ----------------------------------------
 * SeongHeon Sim     2025-04-18
 *
 ****************************************************/
package kyobobook.application.biz.DeliveryOrderInterface.port.in.progress;

import kyobobook.application.adapter.out.persistence.entity.jpa.OrderCommodityId;
import kyobobook.application.domain.commodity.OrderCommodity;
import kyobobook.common.enumeration.OrdrKindCode;

import java.util.List;

/**
 * <AUTHOR> <PERSON><PERSON><PERSON>eon Sim
 * @Project : ord
 * @FileName : NonDeliveryOrderPort
 * @Date : 2025-04-18
 * @description :
 */
public interface NonDeliveryOrderPort {

    /**
     * 주문 상품 및 발급 내역을 기준으로 발급 대상인 주문 상품 ID 를 조회한다.
     *
     * @param orderCommodityIdList 주문 상품 ID 리스트
     * @param ordrKindCodeList     주문 종류 코드 리스트
     * @return 발급 대상 주문 상품 리스트
     */
    List<OrderCommodity> findAllIssuableGiftCardOrderCommodityIdList(List<OrderCommodityId> orderCommodityIdList, List<OrdrKindCode> ordrKindCodeList);


    /**
     * 발급 요청 대상 주문의 결제 데이터가 정상인지 확인한다.
     *
     * @param ordrId 주문 번호
     */
    void validateSuccessfulSettlement(String ordrId);


    /**
     * 기프트카드 발급 및 주문진행상태 변경 처리 (기프트 카드 상품마다 발급 처리 > 수량 무조건 1개인 경우, TOBE)
     *
     * @param issuableOrderCommodityList 기프트 카드 발급 대상 주문 상품 리스트
     */
    void saleGiftCard(List<OrderCommodity> issuableOrderCommodityList);
}
