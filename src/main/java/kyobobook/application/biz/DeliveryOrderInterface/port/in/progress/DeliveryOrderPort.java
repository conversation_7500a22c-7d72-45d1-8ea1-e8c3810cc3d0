package kyobobook.application.biz.DeliveryOrderInterface.port.in.progress;

import kyobobook.application.adapter.out.persistence.entity.jpa.OrderCommodityId;
import kyobobook.application.domain.commodity.OrderCommodity;
import kyobobook.application.domain.event.EventMessage;
import kyobobook.application.domain.memo.OrderMemoList;
import kyobobook.application.domain.order.SettlementMaster;
import kyobobook.common.enumeration.DlvrRspbCode;
import kyobobook.common.enumeration.OrdrKindCode;
import kyobobook.common.enumeration.OrdrPrgsCdtnCode;
import kyobobook.common.enumeration.OrdrStlmPatrCode;
import kyobobook.common.enumeration.StlmMthdCode;

import java.util.List;

/**
 * <AUTHOR> bsjeong7
 * @Project : cm-msg-ord-dlvr
 * @FileName : DeliveryOrderPort
 * @Date : 2024-04-18
 * @description : 배송정보 수신 처리
 */
public interface DeliveryOrderPort {

    /**
     * 배송 진행 상태에 따른 주문 진행 상태 변경
     *
     * @param eventMessage 주문 진행 상태 변경 이벤트 메세지
     */
    void updateOrderProgressCondition(EventMessage eventMessage);

    /**
     * 바로드림 주문의 수령처 변경에 의해 주문 배송지 데이터 및 주문 상품 변경 내역을 업데이트한다.
     *
     * @param ordrId       주문 ID
     * @param ordrSrmb     주문 순번
     * @param ordrDlpnId   주문 배송지 ID
     * @param dlvrRequId   배송 요청 ID
     * @param rcptBranCode 재고지점코드
     * @param addGb        주소구분코드
     * @param rqtrId       요청자 ID
     */
    void updateBarodrimDeliveryInfo(String ordrId, Integer ordrSrmb, String ordrDlpnId, String dlvrRequId, String rcptBranCode, String rcptName, String invnBranCode, String addGb, String rqtrId);

    /**
     * 주문 상품의 주문진행상태코드를 변경한다.
     *
     * @param orderCommodityId 주문 상품 ID
     * @param ordrPrgsCdtnCode 변경 대상 주문진행상태코드
     */
    void updateOrdrPrgsCdtnCode(OrderCommodityId orderCommodityId, OrdrPrgsCdtnCode ordrPrgsCdtnCode);

    /**
     * 주문 상품 ID 리스트, 배송담당코드 기준으로 주문 상품 조회
     *
     * @param orderCommodityIdList 주문 상품 ID 리스트
     * @param ordrPrgsCdtnCode     주문 진행 상태 코드
     * @param dlvrRspbCode         배송 담당 코드
     * @param ordrKindCodeList     주문 종류 코드 리스트
     * @return 주문 상품 리스트
     */
    List<OrderCommodity> findAllOrderCommodityIdAndDlvrRspbCodeAndOrdrKindCode(List<OrderCommodityId> orderCommodityIdList, OrdrPrgsCdtnCode ordrPrgsCdtnCode, DlvrRspbCode dlvrRspbCode, List<OrdrKindCode> ordrKindCodeList);

    /**
     * 외부 결제 연동을 통한 결제 내역을 조회한다.
     *
     * @param ordrId           주문번호
     * @param ordrStlmPatrCode 주문결제구분코드
     * @param stlmMthdCode     결제유형코드
     * @return 결제 내역 리스트
     */
    List<SettlementMaster> findAllByOrdrIdAndOrdrStlmPatrCodeAndStlmAndStlmMthdCode(String ordrId, OrdrStlmPatrCode ordrStlmPatrCode, StlmMthdCode stlmMthdCode);

    /**
     * 주문 번호를 기준으로 등록된 주문 메모를 조회한다.
     *
     * @param ordrId 주문 번호
     * @return 주문 메모 리스트
     */
    OrderMemoList findAllByOrdrId(String ordrId);

    /**
     * 기프트카드 발급 및 주문진행상태 변경 처리 (기프트 카드 상품 중 발급 수량을 기준으로 발급 처리 > 주문 상품 하나에 주문 수량이 여러 개인 경우, ASIS)
     *
     * @param targetIssueOrderCommodityList 기프트 카드 발급 대상 주문 상품 리스트
     * @param issueCount                    발급 수량
     * @param sendMessage                   선물 메세지
     * @param imageCode                     기프트 카드 이미지 코드
     */
    @Deprecated
    void saleGiftCard(List<OrderCommodity> targetIssueOrderCommodityList, long issueCount, String sendMessage, String imageCode);
}
