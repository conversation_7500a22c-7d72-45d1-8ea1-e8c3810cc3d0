/***************************************************
 * Copyright(c) 2021-2022 Kyobo Book Centre All right reserved.
 * This software is the proprietary information of Kyobo Book.
 *
 * Revision History
 * Author                         Date          Description
 * --------------------------     ----------    ----------------------------------------
 * SeongHeon Sim     2024-06-11
 *
 ****************************************************/
package kyobobook.application.biz.DeliveryOrderInterface.handler;

import kyobobook.application.biz.DeliveryOrderInterface.attribute.UpdateBrdrReceiptOfPlaceRequest;
import kyobobook.application.biz.DeliveryOrderInterface.port.in.DeliveryOrderInterfacePort;
import kyobobook.application.biz.DeliveryOrderInterface.port.in.progress.DeliveryOrderPort;
import kyobobook.application.domain.event.EventMessage;
import kyobobook.common.enumeration.DeliveryOrderEvntTypeDvsnCode;
import kyobobook.common.utils.MapperUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR> SeongHeon Sim
 * @Project : ord
 * @FileName : BarodrimReceiptOfHandler
 * @Date : 2024-06-11
 * @description :
 */
@Slf4j
@Service
@RequiredArgsConstructor
class UpdateBrdrReceiptOfPlaceRequestHandler implements DeliveryOrderInterfacePort {

    private final DeliveryOrderPort deliveryOrderPort;

    private final MapperUtils mapperUtils;

    @Override
    public DeliveryOrderEvntTypeDvsnCode getEventType() {
        return DeliveryOrderEvntTypeDvsnCode.BARODRIM_RECEIPT_OF_PLACE_UPDATE;
    }

    @Override
    public void handle(EventMessage eventMessage) {

        // 요청 모델 컨버팅
        var request = mapperUtils.convertRecord(eventMessage.evntCttsCntt(), UpdateBrdrReceiptOfPlaceRequest.class);

        // 바로드림 수령처 변경에 의한 주문 배송지 및 주문 상품 업데이트
        deliveryOrderPort.updateBarodrimDeliveryInfo(
                eventMessage.trgtId(),
                request.ordrSrmb(),
                request.ordrDlpnId(),
                request.dlvrRequId(),
                request.rcptBranCode(),
                request.rcptName(),
                request.invnBranCode(),
                request.addGb(),
                eventMessage.crtrId()
        );
    }
}
