package kyobobook.application.biz.DeliveryOrderInterface.handler;

import com.fasterxml.jackson.core.type.TypeReference;
import kyobobook.application.adapter.out.persistence.entity.jpa.OrderCommodityId;
import kyobobook.application.biz.DeliveryOrderInterface.port.in.DeliveryOrderInterfacePort;
import kyobobook.application.biz.DeliveryOrderInterface.port.in.progress.NonDeliveryOrderPort;
import kyobobook.application.domain.commodity.OrderCommodity;
import kyobobook.application.domain.event.ErrorEventMessage;
import kyobobook.application.domain.event.EventMessage;
import kyobobook.common.enumeration.DeliveryOrderEvntTypeDvsnCode;
import kyobobook.common.enumeration.OrdrKindCode;
import kyobobook.common.utils.MapperUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.context.annotation.Primary;
import org.springframework.stereotype.Service;

import java.util.List;

@Primary
@Service
@RequiredArgsConstructor
@Slf4j
class NonDeliverySaleGiftCardRequestHandlerV2 implements DeliveryOrderInterfacePort {

    private final NonDeliveryOrderPort deliveryOrderPort;
    private final MapperUtils mapperUtils;

    @Override
    public DeliveryOrderEvntTypeDvsnCode getEventType() {
        return DeliveryOrderEvntTypeDvsnCode.NON_DELIVERY_ORDER_SALE_GIFTCARD;
    }

    @Override
    public void handle(EventMessage eventMessage) {
        List<OrderCommodityId> orderCommodityIdList = mapperUtils.convertRecord(eventMessage.evntCttsCntt(), new TypeReference<>() {
        });

        // 1. 정상 승인 여부 확인
        deliveryOrderPort.validateSuccessfulSettlement(eventMessage.trgtId());

        // 2. 발급 대상 주문 상품 목록 조회
        List<OrdrKindCode> ordrKindCodeList = List.of(OrdrKindCode.일반_주문, OrdrKindCode.기프트카드_바로등록_주문, OrdrKindCode.기프트카드_나에게보내기_주문, OrdrKindCode.기프트카드_타인에게보내기_주문);
        List<OrderCommodity> issuableOrderCommodityList = deliveryOrderPort.findAllIssuableGiftCardOrderCommodityIdList(orderCommodityIdList, ordrKindCodeList);

        if (ObjectUtils.isEmpty(issuableOrderCommodityList)) {
            log.info("발급 대상인 기프트 카드 주문 상품이 존재하지 않습니다. ordrId = {}", eventMessage.trgtId());
            return;
        }

        // 3. 기프트 카드 발급 요청 및 주문 진행 상태 코드 업데이트
        deliveryOrderPort.saleGiftCard(issuableOrderCommodityList);
    }

    @Override
    public void errorHandle(ErrorEventMessage errorEventMessage) {
        this.handle(errorEventMessage.kafkaMessage());
    }
}
