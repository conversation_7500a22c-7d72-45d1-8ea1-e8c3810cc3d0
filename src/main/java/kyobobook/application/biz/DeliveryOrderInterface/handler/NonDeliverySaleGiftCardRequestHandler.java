package kyobobook.application.biz.DeliveryOrderInterface.handler;

import com.fasterxml.jackson.core.type.TypeReference;
import kyobobook.application.adapter.out.persistence.entity.jpa.OrderCommodityId;
import kyobobook.application.biz.DeliveryOrderInterface.port.in.DeliveryOrderInterfacePort;
import kyobobook.application.biz.DeliveryOrderInterface.port.in.progress.DeliveryOrderPort;
import kyobobook.application.domain.commodity.OrderCommodity;
import kyobobook.application.domain.event.ErrorEventMessage;
import kyobobook.application.domain.event.EventMessage;
import kyobobook.application.domain.order.SettlementMaster;
import kyobobook.common.enumeration.DeliveryOrderEvntTypeDvsnCode;
import kyobobook.common.enumeration.DlvrRspbCode;
import kyobobook.common.enumeration.OrdrKindCode;
import kyobobook.common.enumeration.OrdrMemoDvsnCode;
import kyobobook.common.enumeration.OrdrPrgsCdtnCode;
import kyobobook.common.enumeration.OrdrStlmPatrCode;
import kyobobook.common.enumeration.StlmMthdCode;
import kyobobook.common.utils.MapperUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;

@Deprecated
@Service
@RequiredArgsConstructor
@Slf4j
class NonDeliverySaleGiftCardRequestHandler implements DeliveryOrderInterfacePort {

    private final DeliveryOrderPort deliveryOrderPort;
    private final MapperUtils mapperUtils;

    @Override
    public DeliveryOrderEvntTypeDvsnCode getEventType() {
        return DeliveryOrderEvntTypeDvsnCode.NON_DELIVERY_ORDER_SALE_GIFTCARD;
    }

    @Override
    public void handle(EventMessage eventMessage) {
        var orderCommodityIdList = mapperUtils.convertRecord(eventMessage.evntCttsCntt(), new TypeReference<List<OrderCommodityId>>() {
        });
        var ordrId = orderCommodityIdList.get(0).getOrdrId();

        // 1. 주문 상품 전체를 조회
        var ordrKindCodeList = List.of(OrdrKindCode.일반_주문, OrdrKindCode.기프트카드_바로등록_주문, OrdrKindCode.기프트카드_나에게보내기_주문, OrdrKindCode.기프트카드_타인에게보내기_주문);
        var orderCommodityList = deliveryOrderPort.findAllOrderCommodityIdAndDlvrRspbCodeAndOrdrKindCode(orderCommodityIdList, OrdrPrgsCdtnCode.SETTLEMENT_FINISH, DlvrRspbCode.ETC, ordrKindCodeList);

        // 1-1. 발급 대상 기프트 카드 주문 상품이 존재하지 않는 경우
        if (orderCommodityList.isEmpty()) {
            log.info("발급 대상인 기프트 카드 주문 상품이 존재하지 않습니다. {}", ordrId);
            return;
        }

        // 2. 기프트 카드 발급 내역 조회
        var giftcardSettlementList = deliveryOrderPort.findAllByOrdrIdAndOrdrStlmPatrCodeAndStlmAndStlmMthdCode(ordrId, OrdrStlmPatrCode.GIFT카드_구매, StlmMthdCode.교보문고기프트카드);
        // 3. 기프트 카드 발급 여부 체크
        var totalIssuedAmount = giftcardSettlementList.stream().mapToLong(SettlementMaster::stlmAmnt).sum(); // 총 발급 완료 금액
        var totalTargetSaleAmount = orderCommodityList.stream().mapToLong(ordrCmdt -> ordrCmdt.cmdtPrceAmnt() * ordrCmdt.requQntt()).sum(); // 총 발급 대상 금액

        // 3-1. 모든 기프트 카드가 발급되었으나 주문진행상태가 업데이트되지 않은 경우
        if (totalIssuedAmount == totalTargetSaleAmount) {
            // 주문 상품이 업데이트가 되지 않은 경우는, 모든 기프트 카드 발급 후 업데이트 처리 과정 중에 예외 발생한 경우
            // 주문 상품 중 수량만큼 발급된 주문 상품만 주문진행상태코드(111 -> 143) 업데이트 및 변경 이력 저장
            orderCommodityList.forEach(orderCommodity -> deliveryOrderPort.updateOrdrPrgsCdtnCode(orderCommodity.getOrderCommodityId(), OrdrPrgsCdtnCode.SENDING_FINISH));
            log.info("모든 기프트 카드가 발급되었습니다. {}", orderCommodityIdList.get(0).getOrdrId());
            return;
        }

        // 4-1. 금액 별 주문 상품 총 수량
        var orderCommodityMap = orderCommodityList.stream().collect(Collectors.groupingBy(OrderCommodity::cmdtPrceAmnt));
        // 4-2. 금액 별 기프트 카드 발급 수량
        var settlementMap = giftcardSettlementList.stream().collect(Collectors.groupingBy(SettlementMaster::stlmAmnt, Collectors.counting()));
        // 4-3. 발송 메세지 및 기프트 카드 이미지 코드 조회(= 주문 메모)
        var orderMemoList = deliveryOrderPort.findAllByOrdrId(ordrId);
        var sendMessage = orderMemoList.getOrdrMemoOrNull(OrdrMemoDvsnCode.GIFT_MESSAGE);
        var imageCode = orderMemoList.getOrdrMemoOrElse(OrdrMemoDvsnCode.GIFT_CARD_IMAGE, () -> "rep0000001");


        // 4-4. 기프트 카드 발급 처리 중 에러가 발생하여 발급하지 못한 수량
        orderCommodityMap.forEach((cmdtPrceAmnt, ordrCmdtList) -> {
            // 현재 금액대의 모든 주문 수량
            var orderQuantity = ordrCmdtList.stream().mapToLong(OrderCommodity::requQntt).sum();
            // 현재 금액대의 발급된 기프트 카드 수량
            var issuedCount = settlementMap.getOrDefault(cmdtPrceAmnt, 0L);
            // 잔여 기프트 카드 수량
            var targetIssueCount = orderQuantity - issuedCount;
            // 발급 대상 수량이 0인 경우 발급 X
            if (targetIssueCount == 0) return;

            // 해당 금액대의 주문 상품 하나만 사용하여 기프트 카드 발급 처리
            deliveryOrderPort.saleGiftCard(ordrCmdtList, targetIssueCount, sendMessage, imageCode);
        });
    }

    @Override
    public void errorHandle(ErrorEventMessage errorEventMessage) {
        this.handle(errorEventMessage.kafkaMessage());
    }
}
