package kyobobook.application.biz.DeliveryOrderInterface.handler;

import kyobobook.application.biz.DeliveryOrderInterface.port.in.DeliveryOrderInterfacePort;
import kyobobook.application.biz.DeliveryOrderInterface.port.in.progress.DeliveryOrderPort;
import kyobobook.application.domain.event.EventMessage;
import kyobobook.common.enumeration.DeliveryOrderEvntTypeDvsnCode;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

@Service
@RequiredArgsConstructor
@Slf4j
class DeliveryEtcConditionRequestHandler implements DeliveryOrderInterfacePort {

    private final DeliveryOrderPort deliveryOrderPort;

    @Override
    public DeliveryOrderEvntTypeDvsnCode getEventType() {
        return DeliveryOrderEvntTypeDvsnCode.NON_DELIVERY_ORDER_TRANS_CONDITION;
    }

    @Override
    public void handle(EventMessage eventMessage) {
        // 비실물, 업체배송 - 배송진행상태 변경에 따른 주문진행상태 변경
        deliveryOrderPort.updateOrderProgressCondition(eventMessage);
    }
}
