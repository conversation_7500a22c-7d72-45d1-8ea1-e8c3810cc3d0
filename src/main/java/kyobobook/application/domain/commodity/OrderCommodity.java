/***************************************************
 * Copyright(c) 2021-2022 Kyobo Book Centre All right reserved.
 * This software is the proprietary information of Kyobo Book.
 *
 * Revision History
 * Author                         Date          Description
 * --------------------------     ----------    ----------------------------------------
 * SeongHeon Sim     2024-04-11
 *
 ****************************************************/
package kyobobook.application.domain.commodity;

import io.micrometer.common.util.StringUtils;
import kyobobook.application.adapter.out.persistence.entity.jpa.OrderCommodityId;
import kyobobook.application.domain.delivery.DeliveryInfo;
import kyobobook.application.domain.memo.OrderMemo;
import kyobobook.application.domain.order.Order;
import kyobobook.common.enumeration.DlvrRspbCode;
import kyobobook.common.enumeration.DlvrShpCode;
import kyobobook.common.enumeration.DlvrShpDtlCode;
import kyobobook.common.enumeration.GiftcrdOrdrPatrCode;
import kyobobook.common.enumeration.OnlnOrdrDtlDvsnCode;
import kyobobook.common.enumeration.OrdrCmdtCnttDvsnCode;
import kyobobook.common.enumeration.OrdrCmdtKindCode;
import kyobobook.common.enumeration.OrdrKindCode;
import kyobobook.common.enumeration.OrdrPrgsCdtnCode;
import kyobobook.common.enumeration.SaleCmdtDvsnCode;
import kyobobook.common.enumeration.SaleCmdtGrpDvsnCode;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR> SeongHeon Sim
 * @Project : fo-order-api
 * @FileName : OrderCommodity
 * @Date : 2024-04-11
 * @description :
 */
public record OrderCommodity(
        String ordrId,
        Integer ordrSrmb,
        Integer ordrCmdtSrmb,
        Integer ordrCmdtProsSrmb,
        Integer ordrCmdtMdfcSrmb,
        OrdrCmdtCnttDvsnCode ordrCmdtMdfcCnttDvsnCode,
        String entsId,
        String unfyCmdtId,
        String saleCmdtId,
        String cmdtName,
        Integer untItmSrmb,
        String untItmName,
        Integer requQntt,
        String sndgPrenDate,
        String txtnDvsnCode,
        Integer cmdtSypr,
        Long cmdtPrceAmnt,
        Long cmdtSlsAmnt,
        Integer untItmSlsAmnt,
        BigDecimal byngRate,
        String cmdtClstCode,
        String joCode,
        String sctnCode,
        String ordrDlpnId,
        String ordrGftRcmsTrgtId,
        Integer ordrGftRcmsTrgtSrmb,
        String addtOrdrCntt,
        String prinCntt,
        Integer ordrCnclQntt,
        Integer rtgdQntt,
        Integer rtgdCnclQntt,
        Integer xchgQntt,
        Integer xchgCnclQntt,
        Boolean lastYsno,
        Boolean fbpYsno,
        Boolean incmDdctTrgtYsno,
        Boolean freeDlvrCmdtYsno,
        Boolean prenCmdtYsno,
        Boolean spciOrdrYsno,
        OrdrCmdtKindCode ordrCmdtKindCode,
        OrdrPrgsCdtnCode ordrPrgsCdtnCode,
        SaleCmdtGrpDvsnCode saleCmdtGrpDvsnCode,
        SaleCmdtDvsnCode saleCmdtDvsnCode,
        OnlnOrdrDtlDvsnCode onlnOrdrDtlDvsnCode,
        DlvrRspbCode dlvrRspbCode,
        DlvrShpCode dlvrShpCode,
        DlvrShpDtlCode dlvrShpDtlCode,
        String crtrId,
        LocalDateTime cretDttm,
        String amnrId,
        LocalDateTime amndDttm,
        boolean dltYsno,
        boolean isModification,
        List<OrderCommodity> moditificationList,
        Order order,
        DeliveryInfo deliveryInfo,
        OrderMemo orderMemo
) {
    public boolean is(OrdrCmdtCnttDvsnCode ordrCmdtCnttDvsnCode) {
        return this.ordrCmdtMdfcCnttDvsnCode == ordrCmdtCnttDvsnCode;
    }

    public boolean in(OrdrCmdtCnttDvsnCode... ordrCmdtCnttDvsnCodeList) {
        return List.of(ordrCmdtCnttDvsnCodeList).contains(this.ordrCmdtMdfcCnttDvsnCode);
    }

    public boolean isBarodrim() {
        return DlvrRspbCode.BUSINESS_PLACE.match(this.dlvrRspbCode) && DlvrShpCode.BARODIRM.match(this.dlvrShpCode);
    }

    public boolean isDigitalContents() {
        return this.saleCmdtGrpDvsnCode == SaleCmdtGrpDvsnCode.SGD
                && SaleCmdtDvsnCode.getDigitalContentsCodeList().contains(this.saleCmdtDvsnCode);
    }

    public boolean isRefundApplicationOrReception() {
        return (this.ordrPrgsCdtnCode == OrdrPrgsCdtnCode.REFUND_APPLICATION || this.ordrPrgsCdtnCode == OrdrPrgsCdtnCode.REFUND_RECEPTION)
                && this.onlnOrdrDtlDvsnCode == OnlnOrdrDtlDvsnCode.REFUND;
    }

    public boolean isExchangeApplicationOrReception() {
        return (this.ordrPrgsCdtnCode == OrdrPrgsCdtnCode.EXCHANGE_APPLICATION || this.ordrPrgsCdtnCode == OrdrPrgsCdtnCode.EXCHANGE_RECEPTION)
                && (this.onlnOrdrDtlDvsnCode == OnlnOrdrDtlDvsnCode.EXCHANGE_DELIVERY || this.onlnOrdrDtlDvsnCode == OnlnOrdrDtlDvsnCode.EXCHANGE_WITHDRAWAL);
    }

    public boolean isCompletedSaleProcessingByDigitalContents() {
        return this.isDigitalContents()
                // SAM
                && ((SaleCmdtDvsnCode.SAM == this.saleCmdtDvsnCode && OrdrPrgsCdtnCode.SETTLEMENT_FINISH == this.ordrPrgsCdtnCode)
                // eBook / 오디오북 / 학술논문 / 북모닝(TODO)
                || (DlvrRspbCode.DIGITAL_CONTENTS == this.dlvrRspbCode && DlvrShpCode.DOWNLOAD == this.dlvrShpCode && OrdrPrgsCdtnCode.STAND_BY == this.ordrPrgsCdtnCode)
                // 컬쳐 상품
                || (SaleCmdtDvsnCode.CUL == this.saleCmdtDvsnCode && OrdrPrgsCdtnCode.SETTLEMENT_FINISH == this.ordrPrgsCdtnCode));
    }

    public String getOrdrDlpnId() {
        if (Objects.isNull(this.deliveryInfo)) {
            return null;
        }

        return this.deliveryInfo.ordrDlpnId();
    }

    public OrderCommodityId getOrderCommodityId() {
        return OrderCommodityId.of(this.ordrCmdtProsSrmb, this.ordrCmdtSrmb, this.ordrId, this.ordrSrmb);
    }

    public boolean equals(String ordrId, Integer ordrSrmb, Integer ordrCmdtSrmb, Integer ordrCmdtProsSrmb) {
        return Objects.equals(this.ordrId, ordrId)
                && Objects.equals(this.ordrSrmb, ordrSrmb)
                && Objects.equals(this.ordrCmdtSrmb, ordrCmdtSrmb)
                && Objects.equals(this.ordrCmdtProsSrmb, ordrCmdtProsSrmb);
    }

    public String getTargetMmbrNumForGFC() {
        // 바로 등록 주문인 경우 회원 번호를 공백으로 리턴한다.
        return Objects.isNull(this.order) || !OrdrKindCode.기프트카드_바로등록_주문.match(this.order.ordrKindCode())
                ? ""
                : this.order.mmbrNum();
    }

    public String getSenderNameForGFC() {
        return Objects.nonNull(this.order) && StringUtils.isNotBlank(this.order.ordrCstmName())
                ? this.order().ordrCstmName()
                : "교보문고";
    }

    public String getSenderPhoneNumberForGFC() {
        return Objects.nonNull(this.order) && StringUtils.isNotBlank(this.order.ordrCstmCphnTlnm())
                ? this.order().ordrCstmCphnTlnm()
                : "1599-1900";
    }

    public String getReceiverNameForGFC() {
        // 주문 정보가 없는 경우 공란으로 리턴
        if (Objects.isNull(this.order)) {
            return "";
        }

        // 수신자명의 기본값은 주문 정보
        var receiverName = this.order.ordrCstmName();

        // 배송지 정보가 존재하는 경우, 배송지 정보에 저장된 수신자명을 기준으로 한다.
        if (Objects.nonNull(this.deliveryInfo)) {
            receiverName = this.deliveryInfo.rcvrName();
        }

        // MMS 전송 내역 기록을 위해 공란으로 리턴, null check 만 진행
        return StringUtils.isNotEmpty(receiverName) ? receiverName : "";
    }

    public String getReceiverPhoneNumberForGFC() {
        // 주문 정보가 없거나 기프트 카드 바로 등록 주문인 경우 공란으로 리턴
        if (Objects.isNull(this.order) || OrdrKindCode.기프트카드_바로등록_주문.match(this.order.ordrKindCode())) {
            return "";
        }

        // 수신자 연락처의 기본값은 주문 정보
        var receiverPhoneNumber = this.order.ordrCstmCphnTlnm();

        // 배송지 정보가 존재하는 경우, 배송지 정보에 저장된 수신자 연락처를 기준으로 한다.
        if (Objects.nonNull(this.deliveryInfo)) {
            receiverPhoneNumber = this.deliveryInfo.rcvrPrtbTlnm();
        }

        // MMS 전송 내역 및 바로 등록 여부 체크를 위해 공란으로 리턴, null check 만 진행
        return StringUtils.isNotEmpty(receiverPhoneNumber) ? receiverPhoneNumber : "";
    }

    public GiftcrdOrdrPatrCode getGiftcrdOrdrPatrCode() {
        return Objects.nonNull(this.order)
                ? GiftcrdOrdrPatrCode.getByOrdrKindCode(this.order.ordrKindCode())
                : GiftcrdOrdrPatrCode.SMO;
    }
}
