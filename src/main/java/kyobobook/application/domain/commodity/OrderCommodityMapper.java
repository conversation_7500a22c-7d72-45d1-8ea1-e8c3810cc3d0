/***************************************************
 * Copyright(c) 2021-2022 Kyobo Book Centre All right reserved.
 * This software is the proprietary information of Kyobo Book.
 *
 * Revision History
 * Author                         Date          Description
 * --------------------------     ----------    ----------------------------------------
 * SeongHeon Sim     2024-04-11
 *
 ****************************************************/
package kyobobook.application.domain.commodity;

import kyobobook.application.adapter.out.persistence.entity.jpa.OrderCommodityEntity;
import kyobobook.application.adapter.out.persistence.entity.jpa.OrderCommodityModificationEntity;
import kyobobook.application.domain.delivery.DeliveryInfo;
import kyobobook.application.domain.delivery.DeliveryInfoMapper;
import kyobobook.application.domain.memo.OrderMemoMapper;
import kyobobook.application.domain.order.Order;
import kyobobook.application.domain.order.OrderMapper;
import org.apache.commons.lang3.StringUtils;

import java.util.Collections;
import java.util.Comparator;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR> SeongHeon Sim
 * @Project : fo-order-api
 * @FileName : OrderCommodityMapper
 * @Date : 2024-04-11
 * @description :
 */
public class OrderCommodityMapper {

    public static OrderCommodity copyOf(OrderCommodityEntity orderCommodityEntity) {

        // 주문 정보가 존재하는 경우 주문 정보 도메인 객체 생성
        Order order = null;
        if (Objects.nonNull(orderCommodityEntity.getOrder())) {
            order = OrderMapper.copyOf(orderCommodityEntity.getOrder());
        }

        // 배송지 정보가 존재하는 경우 배송지 정보 도메인 객체 생성
        DeliveryInfo deliveryInfo = null;
        if (Objects.nonNull(orderCommodityEntity.getDeliveryPoint())) {
            deliveryInfo = DeliveryInfoMapper.copyOf(orderCommodityEntity.getDeliveryPoint());
        }

        return new OrderCommodity(
                orderCommodityEntity.getId().getOrdrId(),
                orderCommodityEntity.getId().getOrdrSrmb(),
                orderCommodityEntity.getId().getOrdrCmdtSrmb(),
                orderCommodityEntity.getId().getOrdrCmdtProsSrmb(),
                null,
                null,
                orderCommodityEntity.getEntsId(),
                orderCommodityEntity.getUnfyCmdtId(),
                orderCommodityEntity.getUnifyCommodity().getSaleCmdtId(),
                orderCommodityEntity.getCmdtName(),
                orderCommodityEntity.getUntItmSrmb(),
                orderCommodityEntity.getUntItmName(),
                orderCommodityEntity.getRequQntt(),
                orderCommodityEntity.getSndgPrenDate(),
                orderCommodityEntity.getTxtnDvsnCode(),
                orderCommodityEntity.getCmdtSypr(),
                orderCommodityEntity.getCmdtPrceAmnt(),
                orderCommodityEntity.getCmdtSlsAmnt(),
                orderCommodityEntity.getUntItmSlsAmnt(),
                orderCommodityEntity.getByngRate(),
                orderCommodityEntity.getCmdtClstCode(),
                orderCommodityEntity.getJoCode(),
                orderCommodityEntity.getSctnCode(),
                orderCommodityEntity.getOrdrDlpnId(),
                orderCommodityEntity.getOrdrGftRcmsTrgtId(),
                orderCommodityEntity.getOrdrGftRcmsTrgtSrmb(),
                orderCommodityEntity.getAddtOrdrCntt(),
                orderCommodityEntity.getPrinCntt(),
                orderCommodityEntity.getOrdrCnclQntt(),
                orderCommodityEntity.getRtgdQntt(),
                orderCommodityEntity.getRtgdCnclQntt(),
                orderCommodityEntity.getXchgQntt(),
                orderCommodityEntity.getXchgCnclQntt(),
                orderCommodityEntity.getLastYsno(),
                orderCommodityEntity.getFbpYsno(),
                orderCommodityEntity.getIncmDdctTrgtYsno(),
                orderCommodityEntity.getFreeDlvrCmdtYsno(),
                orderCommodityEntity.getPrenCmdtYsno(),
                Objects.nonNull(orderCommodityEntity.getForeignOrder()) ? orderCommodityEntity.getForeignOrder().getSpciOrdrYsno() : null,
                orderCommodityEntity.getOrdrCmdtKindCode(),
                orderCommodityEntity.getOrdrPrgsCdtnCode(),
                orderCommodityEntity.getUnifyCommodity().getSaleCmdtGrpDvsnCode(),
                orderCommodityEntity.getSaleCmdtDvsnCode(),
                orderCommodityEntity.getOnlnOrdrDtlDvsnCode(),
                orderCommodityEntity.getDlvrRspbCode(),
                orderCommodityEntity.getDlvrShpCode(),
                orderCommodityEntity.getDlvrShpDtlCode(),
                orderCommodityEntity.getCrtrId(),
                orderCommodityEntity.getCretDttm(),
                orderCommodityEntity.getAmnrId(),
                orderCommodityEntity.getAmndDttm(),
                orderCommodityEntity.getDltYsno(),
                false,
                toList(orderCommodityEntity),
                order,
                deliveryInfo,
                null
        );
    }

    public static List<OrderCommodity> toList(OrderCommodityEntity orderCommodity) {
        var map = orderCommodity.getModificationList()
                .stream()
                .collect(Collectors.groupingBy(OrderCommodityModificationEntity::getOrdrCmdtMdfcCnttDvsnCode));

        return map.values()
                .stream()
                .peek(modificationList -> modificationList.sort(Comparator.comparing(OrderCommodityModificationEntity::getOrdrCmdtMdfcSrmb).reversed()))
                .map(modificationList -> modificationList.stream().findFirst().orElse(null))
                .filter(Objects::nonNull)
                .map(modification -> new OrderCommodity(
                        modification.getId().getOrdrId(),
                        modification.getId().getOrdrSrmb(),
                        modification.getId().getOrdrCmdtSrmb(),
                        modification.getId().getOrdrCmdtProsSrmb(),
                        modification.getOrdrCmdtMdfcSrmb(),
                        modification.getOrdrCmdtMdfcCnttDvsnCode(),
                        orderCommodity.getEntsId(),
                        orderCommodity.getUnfyCmdtId(),
                        orderCommodity.getUnifyCommodity().getSaleCmdtId(),
                        orderCommodity.getCmdtName(),
                        orderCommodity.getUntItmSrmb(),
                        orderCommodity.getUntItmName(),
                        orderCommodity.getRequQntt(),
                        orderCommodity.getSndgPrenDate(),
                        orderCommodity.getTxtnDvsnCode(),
                        orderCommodity.getCmdtSypr(),
                        orderCommodity.getCmdtPrceAmnt(),
                        orderCommodity.getCmdtSlsAmnt(),
                        orderCommodity.getUntItmSlsAmnt(),
                        orderCommodity.getByngRate(),
                        orderCommodity.getCmdtClstCode(),
                        orderCommodity.getJoCode(),
                        orderCommodity.getSctnCode(),
                        orderCommodity.getOrdrDlpnId(),
                        orderCommodity.getOrdrGftRcmsTrgtId(),
                        orderCommodity.getOrdrGftRcmsTrgtSrmb(),
                        orderCommodity.getAddtOrdrCntt(),
                        orderCommodity.getPrinCntt(),
                        orderCommodity.getOrdrCnclQntt(),
                        orderCommodity.getRtgdQntt(),
                        orderCommodity.getRtgdCnclQntt(),
                        orderCommodity.getXchgQntt(),
                        orderCommodity.getXchgCnclQntt(),
                        orderCommodity.getLastYsno(),
                        orderCommodity.getFbpYsno(),
                        orderCommodity.getIncmDdctTrgtYsno(),
                        orderCommodity.getFreeDlvrCmdtYsno(),
                        orderCommodity.getPrenCmdtYsno(),
                        Objects.nonNull(orderCommodity.getForeignOrder()) ? orderCommodity.getForeignOrder().getSpciOrdrYsno() : null,
                        orderCommodity.getOrdrCmdtKindCode(),
                        orderCommodity.getOrdrPrgsCdtnCode(),
                        orderCommodity.getUnifyCommodity().getSaleCmdtGrpDvsnCode(),
                        orderCommodity.getSaleCmdtDvsnCode(),
                        orderCommodity.getOnlnOrdrDtlDvsnCode(),
                        orderCommodity.getDlvrRspbCode(),
                        orderCommodity.getDlvrShpCode(),
                        orderCommodity.getDlvrShpDtlCode(),
                        orderCommodity.getCrtrId(),
                        orderCommodity.getCretDttm(),
                        orderCommodity.getAmnrId(),
                        orderCommodity.getAmndDttm(),
                        orderCommodity.getDltYsno(),
                        true,
                        Collections.emptyList(),
                        null,
                        DeliveryInfoMapper.copyOf(orderCommodity.getDeliveryPoint()),
                        StringUtils.isNotBlank(modification.getOrdrMemoId()) ? OrderMemoMapper.copyOfOrderMemoEntity(modification.getOrderMemo()) : null
                ))
                .toList();
    }
}
