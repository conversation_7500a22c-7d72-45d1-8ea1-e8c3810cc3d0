package kyobobook.application.domain.event;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import kyobobook.common.enumeration.BasicEnum;
import kyobobook.common.enumeration.DeliveryOrderEvntTypeDvsnCode;
import kyobobook.common.enumeration.OrderDeliveryEvntTypeDvsnCode;
import kyobobook.common.enumeration.UnfyOrdrChnlCode;

import java.io.Serializable;
import java.util.LinkedHashMap;
import java.util.Objects;

public record EventMessage(
        /* 이벤트구분코드 */
        String evntTypeDvsnCode,
        /* 주문채널 */
        @JsonDeserialize(using = UnfyOrdrChnlCode.EnumDeserializer.class)
        UnfyOrdrChnlCode unfyOrdrChnlCode,
        /* 이벤트순번 */
        Long evntExecSrmb,
        /* 필수 구분값 (기본적으로 key 와 일치) */
        String trgtId,
        /* 부가정보 (JsonString 형태) */
        String evntCttsCntt,
        /* 생성자 */
        String crtrId
) implements Serializable {

    @JsonIgnore
    public DeliveryOrderEvntTypeDvsnCode getDeliveryOrderEvntTypeDvsnCode() {
        return BasicEnum.findByCode(this.evntTypeDvsnCode, DeliveryOrderEvntTypeDvsnCode.class);
    }

    @JsonIgnore
    public OrderDeliveryEvntTypeDvsnCode getOrderDeliveryEvntTypeDvsnCode() {
        return BasicEnum.findByCode(this.evntTypeDvsnCode, OrderDeliveryEvntTypeDvsnCode.class);
    }

    @JsonIgnore
    public LinkedHashMap<String, String> toAttributes() {
        var subAttributes = new LinkedHashMap<String, String>();
        subAttributes.put("evntTypeDvsnCode", this.evntTypeDvsnCode);
        if (Objects.nonNull(this.unfyOrdrChnlCode)) {
            subAttributes.put("unfyOrdrChnlCode", this.unfyOrdrChnlCode.getCode());
        }
        if (Objects.nonNull(this.evntExecSrmb)) {
            subAttributes.put("evntExecSrmb", this.evntExecSrmb.toString());
        }
        subAttributes.put("trgtId", this.trgtId);
        subAttributes.put("evntCttsCntt", this.evntCttsCntt);
        subAttributes.put("crtrId", this.crtrId);
        return subAttributes;
    }
}
