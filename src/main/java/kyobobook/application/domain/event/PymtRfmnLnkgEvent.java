/***************************************************
 * Copyright(c) 2021-2022 Kyobo Book Centre All right reserved.
 * This software is the proprietary information of Kyobo Book.
 *
 * Revision History
 * Author                         Date          Description
 * --------------------------     ----------    ----------------------------------------
 * <EMAIL>     2025-07-10
 *
 ****************************************************/
package kyobobook.application.domain.event;


import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import kyobobook.config.common.CdcDeletedDeserializer;
import kyobobook.config.common.CdcLocalDateTimeDeserializer;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * <AUTHOR> <EMAIL>
 * @Project : cm-msg-ord-dlvr
 * @FileName : PymtRfmnLnkgEvent
 * @Date : 2025-07-10
 * @description :
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class PymtRfmnLnkgEvent {

    @JsonProperty("ordr_stlm_id")
    private String ordrStlmId;

    @JsonProperty("succs_ysno")
    private String succsYsno;

    @JsonProperty("flr_rsn")
    private String flrRsn;

    @JsonProperty("crtr_id")
    private String crtrId;

    @JsonProperty("cret_dttm")
    @JsonDeserialize(using = CdcLocalDateTimeDeserializer.class)
    private LocalDateTime cretDttm;

    @JsonProperty("amnr_id")
    private String amnrId;

    @JsonProperty("amnd_dttm")
    @JsonDeserialize(using = CdcLocalDateTimeDeserializer.class)
    private LocalDateTime amndDttm;

    @JsonProperty("dlt_ysno")
    private String dltYsno;

    @JsonProperty("__deleted")
    @JsonDeserialize(using = CdcDeletedDeserializer.class)
    private boolean deleted;

    @JsonProperty("__op")
    private String op;

    @JsonProperty("__lsn")
    private long lsn;

    public boolean isCreateOperation() {
        return "c".equalsIgnoreCase(this.op);
    }

    public boolean isUpdateOperation() {
        return "u".equalsIgnoreCase(this.op);
    }
}
