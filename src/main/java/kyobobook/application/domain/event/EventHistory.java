package kyobobook.application.domain.event;

import kyobobook.application.adapter.out.persistence.entity.jpa.OrderEventHistoryEntity;
import kyobobook.common.enumeration.BasicEnum;
import kyobobook.common.enumeration.DeliveryOrderEvntTypeDvsnCode;
import kyobobook.common.enumeration.OrderDeliveryEvntTypeDvsnCode;
import kyobobook.common.enumeration.UnfyOrdrChnlCode;

import java.time.LocalDateTime;

/**
 * DTO for {@link OrderEventHistoryEntity}
 */
public record EventHistory(
        Long evntExecSrmb,
        String ordrId,
        UnfyOrdrChnlCode unfyOrdrChnlCode,
        String evntTypeDvsnCode,
        String evntCttsCntt,
        Boolean evntIssuYsno,
        String crtrId,
        LocalDateTime cretDttm,
        String amnrId,
        LocalDateTime amndDttm,
        Boolean dltYsno
) {

    public DeliveryOrderEvntTypeDvsnCode getDeliveryOrderEvntTypeDvsnCode() {
        return BasicEnum.findByCode(this.evntTypeDvsnCode, DeliveryOrderEvntTypeDvsnCode.class);
    }

    public OrderDeliveryEvntTypeDvsnCode getOrderDeliveryEvntDvsnCode() {
        return BasicEnum.findByCode(this.evntTypeDvsnCode, OrderDeliveryEvntTypeDvsnCode.class);
    }

    public static EventHistory of(OrderEventHistoryEntity entity) {
        return new EventHistory(
                entity.getEvntExecSrmb(),
                entity.getOrdrId(),
                entity.getUnfyOrdrChnlCode(),
                entity.getEvntTypeDvsnCode(),
                entity.getEvntCttsCntt(),
                entity.getEvntIssuYsno(),
                entity.getCrtrId(),
                entity.getCretDttm(),
                entity.getAmnrId(),
                entity.getAmndDttm(),
                entity.getDltYsno()
        );
    }
}
