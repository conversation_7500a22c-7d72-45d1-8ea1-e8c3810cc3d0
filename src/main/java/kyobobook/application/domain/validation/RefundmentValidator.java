/***************************************************
 * Copyright(c) 2021-2022 Kyobo Book Centre All right reserved.
 * This software is the proprietary information of Kyobo Book.
 *
 * Revision History
 * Author                         Date          Description
 * --------------------------     ----------    ----------------------------------------
 * <EMAIL>     2025-07-10
 *
 ****************************************************/
package kyobobook.application.domain.validation;

import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.List;
import java.util.function.Predicate;

/**
 * <AUTHOR> <EMAIL>
 * @Project : cm-msg-ord-dlvr
 * @FileName : RefundmentValidator
 * @Date : 2025-07-10
 * @description : Builder 패턴과 Predicate를 활용한 범용 검증 클래스
 */
@Slf4j
public class RefundmentValidator<T> {

    private final List<ValidationRule<T>> rules;

    private RefundmentValidator(List<ValidationRule<T>> rules) {
        this.rules = rules;
    }

    /**
     * 검증 실행
     */
    public ValidationResult validate(T target) {
        List<String> errors = new ArrayList<>();

        for (ValidationRule<T> rule : rules) {
            if (!rule.getPredicate().test(target)) {
                errors.add(rule.getErrorMessage());
                log.warn("Validation failed: {} for target: {}", rule.getErrorMessage(), target);
            }
        }

        return new ValidationResult(errors.isEmpty(), errors);
    }

    /**
     * Builder 클래스
     */
    public static class Builder<T> {
        private final List<ValidationRule<T>> rules = new ArrayList<>();

        /**
         * 커스텀 검증 규칙 추가
         */
        public Builder<T> addRule(Predicate<T> predicate, String errorMessage) {
            rules.add(new ValidationRule<>(predicate, errorMessage));
            return this;
        }

        /**
         * 조건부 검증 규칙 추가
         */
        public Builder<T> addConditionalRule(Predicate<T> condition,
                                        Predicate<T> validation,
                                        String errorMessage) {
            Predicate<T> conditionalPredicate = target ->
                !condition.test(target) || validation.test(target);
            return addRule(conditionalPredicate, errorMessage);
        }

        /**
         * 복합 검증 규칙 추가 (AND 조건)
         */
        public Builder<T> addAndRule(Predicate<T> predicate1,
                                Predicate<T> predicate2,
                                String errorMessage) {
            return addRule(predicate1.and(predicate2), errorMessage);
        }

        /**
         * 복합 검증 규칙 추가 (OR 조건)
         */
        public Builder<T> addOrRule(Predicate<T> predicate1,
                               Predicate<T> predicate2,
                               String errorMessage) {
            return addRule(predicate1.or(predicate2), errorMessage);
        }

        /**
         * Validator 빌드
         */
        public RefundmentValidator<T> build() {
            return new RefundmentValidator<>(new ArrayList<>(rules));
        }
    }

    /**
     * Builder 인스턴스 생성
     */
    public static <T> Builder<T> builder() {
        return new Builder<>();
    }

    /**
     * 검증 규칙 클래스
     */
    private static class ValidationRule<T> {
        private final Predicate<T> predicate;
        private final String errorMessage;

        public ValidationRule(Predicate<T> predicate, String errorMessage) {
            this.predicate = predicate;
            this.errorMessage = errorMessage;
        }

        public Predicate<T> getPredicate() {
            return predicate;
        }

        public String getErrorMessage() {
            return errorMessage;
        }
    }

    /**
     * 검증 결과 클래스
     */
    public static class ValidationResult {
        private final boolean valid;
        private final List<String> errors;

        public ValidationResult(boolean valid, List<String> errors) {
            this.valid = valid;
            this.errors = errors;
        }

        public boolean isValid() {
            return valid;
        }

        public List<String> getErrors() {
            return errors;
        }

        public String getErrorMessage() {
            return String.join(", ", errors);
        }
    }
}
