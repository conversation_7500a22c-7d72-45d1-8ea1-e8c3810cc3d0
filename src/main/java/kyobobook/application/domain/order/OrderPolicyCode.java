/***************************************************
 * Copyright(c) 2021-2022 Kyobo Book Centre All right reserved.
 * This software is the proprietary information of Kyobo Book.
 *
 * Revision History
 * Author                         Date          Description
 * --------------------------     ----------    ----------------------------------------
 * SeongHeon Sim     2024-05-23
 *
 ****************************************************/
package kyobobook.application.domain.order;

import kyobobook.application.adapter.out.persistence.entity.jpa.OrderPolicyCodeEntity;
import kyobobook.common.enumeration.BasicEnum;
import kyobobook.common.exception.BizRuntimeException;
import org.springframework.util.CollectionUtils;

import java.util.EnumSet;
import java.util.List;

/**
 * <AUTHOR> SeongHeon Sim
 * @Project : ord
 * @FileName : OrderPolicyCode
 * @Date : 2024-05-23
 * @description :
 */
public record OrderPolicyCode(
        String codeId,
        String codeWrth,
        Remark remark_1,
        Remark remark_2,
        Remark remark_3,
        Remark remark_4,
        Remark remark_5,
        Remark remark_6,
        Remark remark_7,
        Remark remark_8,
        Remark remark_9,
        Remark remark_10,
        Remark remark_11,
        Remark remark_12,
        Remark remark_13,
        Remark remark_14,
        Remark remark_15
) {

    public static OrderPolicyCode of(OrderPolicyCodeEntity entity) {
        return new OrderPolicyCode(
                entity.getId().getCodeId(),
                entity.getId().getCodeWrth(),
                new Remark(entity.getRemark_1()),
                new Remark(entity.getRemark_2()),
                new Remark(entity.getRemark_3()),
                new Remark(entity.getRemark_4()),
                new Remark(entity.getRemark_5()),
                new Remark(entity.getRemark_6()),
                new Remark(entity.getRemark_7()),
                new Remark(entity.getRemark_8()),
                new Remark(entity.getRemark_9()),
                new Remark(entity.getRemark_10()),
                new Remark(entity.getRemark_11()),
                new Remark(entity.getRemark_12()),
                new Remark(entity.getRemark_13()),
                new Remark(entity.getRemark_14()),
                new Remark(entity.getRemark_15())
        );
    }

    public record Remark(
            List<String> remark
    ) {

        public <T extends Enum<T> & BasicEnum<E>, E> T findEnum(int index, Class<T> clazz) {

            if (CollectionUtils.isEmpty(this.remark) || remark.size() < index) {
                throw new BizRuntimeException("찾고자하는 주문 정책 코드가 존재하지 않습니다.");
            }

            var code = this.remark.get(index);

            return EnumSet.allOf(clazz)
                    .stream()
                    .filter(e -> e.getCode().equals(code))
                    .findFirst().orElseThrow(() -> new BizRuntimeException("찾고자하는 주문 정책 코드가 존재하지 않습니다."));
        }
    }
}
