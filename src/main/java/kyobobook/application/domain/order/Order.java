/***************************************************
 * Copyright(c) 2021-2022 Kyobo Book Centre All right reserved.
 * This software is the proprietary information of Kyobo Book.
 *
 * Revision History
 * Author                         Date          Description
 * --------------------------     ----------    ----------------------------------------
 * SeongHeon Sim     2024-05-22
 *
 ****************************************************/
package kyobobook.application.domain.order;

import kyobobook.common.enumeration.OnlnOrdrDvsnCode;
import kyobobook.common.enumeration.OrdrKindCode;
import kyobobook.common.enumeration.OrdrProsRsltCode;
import kyobobook.common.enumeration.UnfyOrdrChnlCode;

/**
 * <AUTHOR> <PERSON><PERSON><PERSON><PERSON> Sim
 * @Project : ord
 * @FileName : Order
 * @Date : 2024-05-22
 * @description :
 */
public record Order(
        String ordrId,
        Integer ordrSrmb,
        String mmbrNum,
        String ordrCstmName,
        String ordrCstmCphnTlnm,
        String stlmFnshDate,
        String stlmFnshHms,
        OnlnOrdrDvsnCode onlnOrdrDvsnCode,
        OrdrProsRsltCode ordrProsRsltCode,
        UnfyOrdrChnlCode unfyOrdrChnlCode,
        OrdrKindCode ordrKindCode,
        Integer fndgPrjtSrmb
) {

    public boolean isCimsOrder() {
        return UnfyOrdrChnlCode.CIMS_ORDER == this.unfyOrdrChnlCode;
    }
}
