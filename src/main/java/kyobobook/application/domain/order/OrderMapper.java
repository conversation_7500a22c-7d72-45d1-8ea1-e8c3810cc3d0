/***************************************************
 * Copyright(c) 2021-2022 Kyobo Book Centre All right reserved.
 * This software is the proprietary information of Kyobo Book.
 *
 * Revision History
 * Author                         Date          Description
 * --------------------------     ----------    ----------------------------------------
 * SeongHeon Sim     2024-05-22
 *
 ****************************************************/
package kyobobook.application.domain.order;

import kyobobook.application.adapter.out.persistence.entity.jpa.OrderEntity;

/**
 * <AUTHOR> SeongHeon Sim
 * @Project : ord
 * @FileName : OrderMapper
 * @Date : 2024-05-22
 * @description :
 */
public class OrderMapper {

    public static Order copyOf(OrderEntity order) {
        return new Order(
                order.getId().getOrdrId(),
                order.getId().getOrdrSrmb(),
                order.getMmbrNum(),
                order.getOrdrCstmName(),
                order.getOrdrCstmCphnTlnm(),
                order.getStlmFnshDate(),
                order.getStlmFnshHms(),
                order.getOnlnOrdrDvsnCode(),
                order.getOrdrProsRsltCode(),
                order.getUnfyOrdrChnlCode(),
                order.getOrdrKindCode(),
                order.getFndgPrjtSrmb()
        );
    }
}
