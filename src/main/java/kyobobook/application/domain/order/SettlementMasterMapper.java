/***************************************************
 * Copyright(c) 2021-2022 Kyobo Book Centre All right reserved.
 * This software is the proprietary information of Kyobo Book.
 *
 * Revision History
 * Author                         Date          Description
 * --------------------------     ----------    ----------------------------------------
 * SeongHeon Sim     2024-09-19
 *
 ****************************************************/
package kyobobook.application.domain.order;

import kyobobook.application.adapter.out.persistence.entity.jpa.SettlementMasterEntity;

/**
 * <AUTHOR> SeongHeon Sim
 * @Project : ord
 * @FileName : SettlementMasterMapper
 * @Date : 2024-09-19
 * @description :
 */
public class SettlementMasterMapper {

    public static SettlementMaster copyOf(SettlementMasterEntity settlementMaster) {
        return new SettlementMaster(
                settlementMaster.getOrdrStlmNum(),
                settlementMaster.getStlmDate(),
                settlementMaster.getStlmTrncNum(),
                settlementMaster.getOrdrStlmPatrCode(),
                settlementMaster.getStlmAproPatrCode(),
                settlementMaster.getUnfyOrdrChnlCode(),
                settlementMaster.getOrdrId(),
                settlementMaster.getStlmMthdCode(),
                settlementMaster.getPgAproDttm(),
                settlementMaster.getPgAproNum(),
                settlementMaster.getStlmAmnt(),
                settlementMaster.getStlmBlce(),
                settlementMaster.getAcmlTrgtAmnt(),
                settlementMaster.getMmbrNum(),
                settlementMaster.getMarketId(),
                settlementMaster.getStlmLnkgCrttCntt(),
                settlementMaster.getStlmLnkgAddtCntt(),
                settlementMaster.getLastAplYsno(),
                settlementMaster.getCstmTlnm(),
                settlementMaster.getAproCnclPsblYsno()
        );
    }
}
