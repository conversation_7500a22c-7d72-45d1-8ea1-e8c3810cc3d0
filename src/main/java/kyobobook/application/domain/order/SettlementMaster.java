package kyobobook.application.domain.order;

import kyobobook.common.enumeration.OrdrStlmPatrCode;
import kyobobook.common.enumeration.StlmAproPatrCode;
import kyobobook.common.enumeration.StlmMthdCode;
import kyobobook.common.enumeration.UnfyOrdrChnlCode;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;

/***************************************************
 * Copyright(c) 2021-2022 Kyobo Book Centre All right reserved.
 * This software is the proprietary information of Kyobo Book.
 *
 * Revision History
 * Author                         Date          Description
 * --------------------------     ----------    ----------------------------------------
 * SeongHeon Sim     2024-09-19
 *
 ****************************************************/
public record SettlementMaster(
        String ordrStlmNum,
        String stlmDate,
        String stlmTrncNum,
        OrdrStlmPatrCode ordrStlmPatrCode,
        StlmAproPatrCode stlmAproPatrCode,
        UnfyOrdrChnlCode unfyOrdrChnlCode,
        String ordrId,
        StlmMthdCode stlmMthdCode,
        String pgAproDttm,
        String pgAproNum,
        Long stlmAmnt,
        Long stlmBlce,
        Long acmlTrgtAmnt,
        String mmbrNum,
        String marketId,
        String stlmLnkgCrttCntt,
        String stlmLnkgAddtCntt,
        Boolean lastAplYsno,
        String cstmTlnm,
        Boolean aproCnclPsblYsno
) {

    public boolean isGiftCardSettlement() {
        return UnfyOrdrChnlCode.UNIFY_ORDER.match(this.unfyOrdrChnlCode) && OrdrStlmPatrCode.GIFT카드_구매.match(this.ordrStlmPatrCode);
    }

    public boolean isSettlementFinishWithin7Days() {
        var settlementDate = LocalDate.parse(this.stlmDate, DateTimeFormatter.ofPattern("yyyyMMdd"));
        var now = LocalDate.now();
        var sevenDaysAgo = now.minusDays(7);
        return (settlementDate.isAfter(sevenDaysAgo) || settlementDate.isEqual(sevenDaysAgo)) && (settlementDate.isBefore(now) || settlementDate.isEqual(now));
    }

    public boolean isCanceled() {
        return this.lastAplYsno && StlmAproPatrCode.승인취소.match(this.stlmAproPatrCode);
    }
}
