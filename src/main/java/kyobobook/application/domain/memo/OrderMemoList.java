/***************************************************
 * Copyright(c) 2021-2022 Kyobo Book Centre All right reserved.
 * This software is the proprietary information of Kyobo Book.
 *
 * Revision History
 * Author                         Date          Description
 * --------------------------     ----------    ----------------------------------------
 * SeongHeon Sim     2025-04-15
 *
 ****************************************************/
package kyobobook.application.domain.memo;

import kyobobook.common.enumeration.OrdrMemoDvsnCode;
import org.apache.commons.lang3.StringUtils;
import org.springframework.util.ObjectUtils;

import java.util.List;
import java.util.function.Supplier;

/**
 * <AUTHOR> SeongHeon Sim
 * @Project : ord
 * @FileName : OrderMemoList
 * @Date : 2025-04-15
 * @description :
 */
public record OrderMemoList(
        String ordrId,
        List<OrderMemo> orderMemoList
) {
    public String getOrdrMemoOrNull(OrdrMemoDvsnCode ordrMemoDvsnCode) {
        if (ObjectUtils.isEmpty(this.orderMemoList)) {
            return null;
        }

        return orderMemoList.stream()
                .filter(orderMemo -> orderMemo.ordrMemoDvsnCode().match(ordrMemoDvsnCode))
                .findFirst()
                .map(OrderMemo::ordrMemo)
                .orElse(null);
    }

    public String getOrdrMemoOrElse(OrdrMemoDvsnCode ordrMemoDvsnCode, Supplier<String> messageSupplier) {
        if (ObjectUtils.isEmpty(this.orderMemoList)) {
            return messageSupplier.get();
        }

        return orderMemoList.stream()
                .filter(orderMemo -> orderMemo.ordrMemoDvsnCode().match(ordrMemoDvsnCode))
                .findFirst()
                .map(OrderMemo::ordrMemo)
                .filter(StringUtils::isNotBlank)
                .orElseGet(messageSupplier);
    }
}
