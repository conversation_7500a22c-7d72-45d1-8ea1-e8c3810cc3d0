/***************************************************
 * Copyright(c) 2021-2022 Kyobo Book Centre All right reserved.
 * This software is the proprietary information of Kyobo Book.
 *
 * Revision History
 * Author                         Date          Description
 * --------------------------     ----------    ----------------------------------------
 * SeongHeon Sim     2024-04-29
 *
 ****************************************************/
package kyobobook.application.domain.memo;

import kyobobook.common.enumeration.OrdrMemoDvsnCode;

import java.time.LocalDateTime;

/**
 * <AUTHOR> SeongHeon Sim
 * @Project : ord
 * @FileName : OrderMemo
 * @Date : 2024-04-29
 * @description :
 */
public record OrderMemo(
        String ordrMemoId,
        String ordrMemo,
        OrdrMemoDvsnCode ordrMemoDvsnCode,
        String ordrId,
        Integer ordrSrmb,
        String ordrDlpnId,
        String ordrGftRcmsTrgtId,
        Integer ordrGftRcmsTrgtSrmb,
        String gftMsgCardThemeCode,
        String crtrId,
        LocalDateTime cretDttm,
        String amnrId,
        LocalDateTime amndDttm,
        boolean isModification
) {

    public static OrderMemo of(
            String ordrMemoId,
            String ordrMemo,
            OrdrMemoDvsnCode ordrMemoDvsnCode,
            String ordrId,
            Integer ordrSrmb,
            String ordrDlpnId,
            String ordrGftRcmsTrgtId,
            Integer ordrGftRcmsTrgtSrmb,
            String gftMsgCardThemeCode,
            String crtrId,
            LocalDateTime cretDttm,
            String amnrId,
            LocalDateTime amndDttm,
            boolean isModification
    ) {
        return new OrderMemo(
                ordrMemoId,
                ordrMemo,
                ordrMemoDvsnCode,
                ordrId,
                ordrSrmb,
                ordrDlpnId,
                ordrGftRcmsTrgtId,
                ordrGftRcmsTrgtSrmb,
                gftMsgCardThemeCode,
                crtrId,
                cretDttm,
                amnrId,
                amndDttm,
                isModification
        );
    }

    public static OrderMemo emptyMemoCopyOf(OrderMemo orderMemo) {
        return new OrderMemo(
                orderMemo.ordrMemoId(),
                "",
                orderMemo.ordrMemoDvsnCode,
                orderMemo.ordrId,
                orderMemo.ordrSrmb,
                orderMemo.ordrDlpnId,
                orderMemo.ordrGftRcmsTrgtId,
                orderMemo.ordrGftRcmsTrgtSrmb,
                orderMemo.gftMsgCardThemeCode,
                orderMemo.crtrId,
                orderMemo.cretDttm,
                orderMemo.amnrId,
                orderMemo.amndDttm,
                orderMemo.isModification
        );
    }
}
