/***************************************************
 * Copyright(c) 2021-2022 Kyobo Book Centre All right reserved.
 * This software is the proprietary information of Kyobo Book.
 *
 * Revision History
 * Author                         Date          Description
 * --------------------------     ----------    ----------------------------------------
 * SeongHeon Sim     2024-04-30
 *
 ****************************************************/
package kyobobook.application.domain.memo;

import kyobobook.application.adapter.out.persistence.entity.jpa.OrderDeliveryPointEntity;
import kyobobook.application.adapter.out.persistence.entity.jpa.OrderMemoEntity;
import org.apache.commons.lang3.StringUtils;
import org.springframework.util.CollectionUtils;

import java.util.Objects;

/**
 * <AUTHOR> SeongHeon Sim
 * @Project : ord
 * @FileName : OrderMemoMapper
 * @Date : 2024-04-30
 * @description :
 */
public class OrderMemoMapper {

    public static OrderMemo copyOfOrderMemoEntity(OrderMemoEntity orderMemo) {

        // ordrMemoId 가 공백으로 있는 경우가 있어 해당 부분에서 제외 처리 하도록 한다.
        if (Objects.isNull(orderMemo) || StringUtils.isBlank(orderMemo.getOrdrMemoId()) || orderMemo.getDltYsno()) {
            return null;
        }

        return OrderMemo.of(
                orderMemo.getOrdrMemoId(),
                orderMemo.getOrdrMemo(),
                orderMemo.getOrdrMemoDvsnCode(),
                orderMemo.getOrdrId(),
                orderMemo.getOrdrSrmb(),
                orderMemo.getOrdrDlpnId(),
                orderMemo.getOrdrGftRcmsTrgtId(),
                orderMemo.getOrdrGftRcmsTrgtSrmb(),
                orderMemo.getGftMsgCardThemeCode(),
                orderMemo.getCrtrId(),
                orderMemo.getCretDttm(),
                orderMemo.getAmnrId(),
                orderMemo.getAmndDttm(),
                !CollectionUtils.isEmpty(orderMemo.getModificationList())
        );
    }

    public static OrderMemoEntity createNewEntity(String newOrdrMemoId, OrderMemo orderMemo, OrderDeliveryPointEntity deliveryPoint, String rqtrId) {
        return OrderMemoEntity.createNewInstance(
                newOrdrMemoId,
                orderMemo.ordrMemoDvsnCode(),
                orderMemo.ordrMemo(),
                orderMemo.ordrId(),
                orderMemo.ordrSrmb(),
                orderMemo.ordrGftRcmsTrgtId(),
                orderMemo.ordrGftRcmsTrgtSrmb(),
                orderMemo.gftMsgCardThemeCode(),
                deliveryPoint,
                rqtrId
        );
    }
}
