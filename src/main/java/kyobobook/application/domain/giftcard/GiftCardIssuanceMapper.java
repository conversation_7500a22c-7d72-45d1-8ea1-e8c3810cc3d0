/***************************************************
 * Copyright(c) 2021-2022 Kyobo Book Centre All right reserved.
 * This software is the proprietary information of Kyobo Book.
 *
 * Revision History
 * Author                         Date          Description
 * --------------------------     ----------    ----------------------------------------
 * SeongHeon Sim     2025-03-14
 *
 ****************************************************/
package kyobobook.application.domain.giftcard;

import kyobobook.application.adapter.out.persistence.entity.jpa.GiftCardIssuanceEntity;
import kyobobook.application.adapter.out.persistence.entity.jpa.GiftCardRegistrationEntity;
import kyobobook.application.adapter.out.persistence.entity.jpa.GiftCardSettlementDetailEntity;
import kyobobook.application.adapter.out.persistence.entity.jpa.OrderCommodityEntity;
import kyobobook.application.domain.commodity.OrderCommodity;
import kyobobook.application.domain.commodity.OrderCommodityMapper;

import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR> SeongHeon Sim
 * @Project : ord
 * @FileName : GiftCardIssuanceMapper
 * @Date : 2025-03-14
 * @description :
 */
public class GiftCardIssuanceMapper {

    public static GiftCardIssuance copyOf(
            GiftCardIssuanceEntity giftCardIssuanceEntity,
            GiftCardSettlementDetailEntity giftCardSettlementDetailEntity,
            OrderCommodityEntity orderCommodityEntity,
            List<GiftCardRegistrationEntity> giftCardRegistrationEntityList
    ) {
        // 결제 기본 내역
        GiftCardSettlementDetail giftCardSettlementDetail = null;
        if (Objects.nonNull(giftCardSettlementDetailEntity)) {
            giftCardSettlementDetail = GiftCardSettlementDetailMapper.copyOf(giftCardSettlementDetailEntity, giftCardRegistrationEntityList);
        }

        // 주문 상품
        OrderCommodity orderCommodity = null;
        if (Objects.nonNull(orderCommodityEntity)) {
            orderCommodity = OrderCommodityMapper.copyOf(orderCommodityEntity);
        }

        return new GiftCardIssuance(
                giftCardIssuanceEntity.getId().getEncrGiftcrdNum(),
                giftCardIssuanceEntity.getId().getGiftcrdCrttNum(),
                giftCardIssuanceEntity.getId().getGiftcrdIsncSrmb(),
                giftCardIssuanceEntity.getGiftcrdOrdrPatrCode(),
                giftCardIssuanceEntity.getGiftcrdIsncCdtnCode(),
                giftCardIssuanceEntity.getOrdrStlmNum(),
                giftCardIssuanceEntity.getOrdrId(),
                giftCardIssuanceEntity.getOrdrCmdtSrmb(),
                giftCardIssuanceEntity.getCretDttm(),
                giftCardSettlementDetail,
                orderCommodity
        );
    }
}
