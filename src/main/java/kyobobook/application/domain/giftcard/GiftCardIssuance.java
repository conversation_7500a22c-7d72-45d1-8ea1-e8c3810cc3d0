package kyobobook.application.domain.giftcard;

import kyobobook.application.domain.commodity.OrderCommodity;
import kyobobook.common.enumeration.GiftcrdIsncCdtnCode;
import kyobobook.common.enumeration.GiftcrdOrdrPatrCode;
import kyobobook.common.enumeration.OrdrPrgsCdtnCode;
import kyobobook.common.enumeration.StlmAproPatrCode;

import java.time.LocalDateTime;

/***************************************************
 * Copyright(c) 2021-2022 Kyobo Book Centre All right reserved.
 * This software is the proprietary information of Kyobo Book.
 *
 * Revision History
 * Author                         Date          Description
 * --------------------------     ----------    ----------------------------------------
 * SeongHeon Sim     2025-03-14
 *
 ****************************************************/
public record GiftCardIssuance(
        String encrGiftcrdNum,
        String giftcrdCrttNum,
        Long giftcrdIsncSrmb,
        GiftcrdOrdrPatrCode giftcrdOrdrPatrCode,
        GiftcrdIsncCdtnCode giftcrdIsncCdtnCode,
        String ordrStlmNum,
        String ordrId,
        Integer ordrCmdtSrmb,
        LocalDateTime cretDttm,
        GiftCardSettlementDetail giftCardSettlementDetail,
        OrderCommodity orderCommodity
) {

    public boolean isCancellationExpired() {
        // 상태가 [발급/발신]이면서 데이터 생성일이 7일이 지난 경우
        return (GiftcrdIsncCdtnCode.CANCELABLE_LIST.contains(this.giftcrdIsncCdtnCode) && this.cretDttm.plusDays(7L).isBefore(LocalDateTime.now()))
                // 결제 기본 데이터가 취소가 아니면서 결제 기본 데이터의 승인일자가 7일이 지난 경우
                || (!this.giftCardSettlementDetail.settlementMaster().isCanceled() && !this.giftCardSettlementDetail.settlementMaster().isSettlementFinishWithin7Days());
    }

    public boolean isCancelable() {
        return GiftcrdIsncCdtnCode.CANCELABLE_LIST.contains(this.giftcrdIsncCdtnCode) // 발급/발신 상태인 경우
                && StlmAproPatrCode.승인.match(this.giftCardSettlementDetail.settlementMaster().stlmAproPatrCode()) // 결제 기본이 승인 상태인 경우
                && OrdrPrgsCdtnCode.SENDING_FINISH.match(this.orderCommodity.ordrPrgsCdtnCode()) // 주문 진행 상태가 발송 완료(143)인 경우
                && this.orderCommodity.requQntt() - this.orderCommodity.ordrCnclQntt() > 0 // 취소 가능 수량이 1개 이상인 경우
                && !this.giftCardSettlementDetail().hasOwner(); // 기프트 카드 소유자가 존재하지 않는 경우
    }

    public Long getCancelableAmount() {
        // 취소가 불가능한 경우 취소 가능 금액은 0원으로 리턴
        if (!this.isCancelable()) {
            return 0L;
        }
        // 기프트 카드 발급 금액을 리턴하도록 한다.
        return this.giftCardSettlementDetail.settlementMaster().stlmAmnt();
    }
}
