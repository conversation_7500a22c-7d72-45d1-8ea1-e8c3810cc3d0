/***************************************************
 * Copyright(c) 2021-2022 Kyobo Book Centre All right reserved.
 * This software is the proprietary information of Kyobo Book.
 *
 * Revision History
 * Author                         Date          Description
 * --------------------------     ----------    ----------------------------------------
 * SeongHeon Sim     2025-03-21
 *
 ****************************************************/
package kyobobook.application.domain.giftcard;

import kyobobook.common.enumeration.StlmAproPatrCode;
import kyobobook.common.enumeration.StlmMthdCode;
import org.springframework.util.ObjectUtils;

import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * <AUTHOR> SeongHeon Sim
 * @Project : ord
 * @FileName : GiftCardIssuanceLIst
 * @Date : 2025-03-21
 * @description :
 */
public record GiftCardSettlementDetailList(
        String ordrId,
        List<GiftCardSettlementDetail> settlementDetailList
) {
    public Map<Long, List<GiftCardSettlementDetail>> convertToMapByStlmAmnt() {
        return this.settlementDetailList.stream()
                .filter(giftCardSettlementDetail -> StlmAproPatrCode.승인.match(giftCardSettlementDetail.settlementMaster().stlmAproPatrCode())
                        && giftCardSettlementDetail.settlementMaster().stlmMthdCode().anyMatch(List.of(StlmMthdCode.교보문고기프트카드, StlmMthdCode.기업기프트카드)))
                .collect(Collectors.groupingBy(detail -> detail.settlementMaster().stlmAmnt()));
    }

    public Optional<GiftCardSettlementDetail> getLastGiftCardSettlementDetail() {
        if (ObjectUtils.isEmpty(this.settlementDetailList)) {
            return Optional.empty();
        }

        return this.settlementDetailList.stream().max(Comparator.comparing(GiftCardSettlementDetail::ordrStlmNum));
    }
}
