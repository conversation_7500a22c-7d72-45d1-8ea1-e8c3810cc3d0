/***************************************************
 * Copyright(c) 2021-2022 Kyobo Book Centre All right reserved.
 * This software is the proprietary information of Kyobo Book.
 *
 * Revision History
 * Author                         Date          Description
 * --------------------------     ----------    ----------------------------------------
 * SeongHeon Sim     2025-03-21
 *
 ****************************************************/
package kyobobook.application.domain.giftcard;

import kyobobook.application.adapter.out.persistence.entity.jpa.OrderCommodityId;
import kyobobook.application.domain.commodity.OrderCommodity;
import org.apache.commons.lang3.ObjectUtils;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.function.BinaryOperator;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR> SeongHeon Sim
 * @Project : ord
 * @FileName : GiftCardIssuanceLIst
 * @Date : 2025-03-21
 * @description :
 */
public record GiftCardIssuanceList(
        String ordrId,
        List<GiftCardIssuance> issuanceList
) {
    public BigDecimal totalCancelableAmount() {
        var totalCancelAmount = this.issuanceList.stream().mapToLong(GiftCardIssuance::getCancelableAmount).sum();
        return BigDecimal.valueOf(totalCancelAmount);
    }

    public Map<Integer, GiftCardIssuanceList> lastIssuanceListToMapByOrdrCmdtSrmb() {
        return this.issuanceList.stream().collect(Collectors.groupingBy(
                GiftCardIssuance::ordrCmdtSrmb,
                Collectors.collectingAndThen(
                        Collectors.toMap(GiftCardIssuance::encrGiftcrdNum, Function.identity(), BinaryOperator.maxBy(Comparator.comparing(GiftCardIssuance::giftcrdIsncSrmb))),
                        m -> new GiftCardIssuanceList(this.ordrId, new ArrayList<>(m.values()))
                )
        ));
    }

    public List<OrderCommodityId> getIssuedOrderCommodityId() {
        if (this.issuanceList.isEmpty()) {
            return Collections.emptyList();
        }

        // 기프트 카드 교환인 경우 OrderCommodity 가 없으니 null 로 설정되기 때문에 기프트 카드 주문에 한해서 데이터가 추출된다.
        return this.issuanceList.stream()
                .map(GiftCardIssuance::orderCommodity)
                .filter(ObjectUtils::isNotEmpty)
                .map(OrderCommodity::getOrderCommodityId)
                .toList();

    }
}
