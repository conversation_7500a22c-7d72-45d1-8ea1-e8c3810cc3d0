/***************************************************
 * Copyright(c) 2021-2022 Kyobo Book Centre All right reserved.
 * This software is the proprietary information of Kyobo Book.
 *
 * Revision History
 * Author                         Date          Description
 * --------------------------     ----------    ----------------------------------------
 * SeongHeon Sim     2024-04-08
 *
 ****************************************************/
package kyobobook.application.domain.delivery;

import kyobobook.common.enumeration.DlvrAdrsDvsnCode;
import org.apache.commons.lang3.StringUtils;

/**
 * <AUTHOR> <PERSON>ong<PERSON>eon Sim
 * @Project : fo-order-api
 * @FileName : DeliveryPoint
 * @Date : 2024-04-08
 * @description :
 */
public record DeliveryInfo(

        // 주문배송지ID
        String ordrDlpnId,

        // 주문ID
        String ordrId,

        // 회원번호
        String mmbrNum,

        // 배송지명
        String dlpnName,

        // 배송 수단 구분 코드
        String dlvrMthdDvsnCode,

        // 수취인명
        String rcvrName,

        // 수취인전화번호
        String rcvrTlnm,

        // 수취인휴대전화번호
        String rcvrPrtbTlnm,

        // 수령지점코드
        String rcptBranCode,

        // 재고지점코드
        String invnBranCode,

        // 해외배송업체코드
        String frngDscmCode,

        // 주소구분코드
        DlvrAdrsDvsnCode dlvrAdrsDvsnCode,

        // 상세주소1
        String dtlAdrs1,

        // 상세주소2
        String dtlAdrs2,

        // 상세주소3
        String dtlAdrs3,

        // 상세주소4
        String dtlAdrs4,

        // 상세주소5
        String dtlAdrs5,

        // 상세주소6
        String dtlAdrs6,

        // 상세주소7
        String dtlAdrs7,

        // 상세주소8
        String dtlAdrs8,

        // 상세주소9
        String dtlAdrs9,

        // 상세주소10
        String dtlAdrs10,

        // 상세주소11
        String dtlAdrs11,

        // 상세주소12
        String dtlAdrs12,

        // 개인통관고유번호
        String prsnEntyChaNum,

        // 선물영수증가격노출여부
        boolean gftRecpPricExprYsno,

        // 착한배송선택여부
        boolean gddlSlctYsno,

        // 바로드림 픽업 박스 여부
        boolean brdrPickupboxYsno,

        // 생성자ID
        String crtrId,

        // 생성일시
        String cretDttm,

        // 수정자ID
        String amnrId,

        // 수정일시
        String amndDttm,

        // 삭제여부
        boolean dltYsno,

        // 요청자 ID
        String rqtrId
) {

    public static DeliveryInfo of(
            String ordrDlpnId,
            String ordrId,
            String mmbrNum,
            String dlpnName,
            String dlvrMthdDvsnCode,
            String rcvrName,
            String rcvrTlnm,
            String rcvrPrtbTlnm,
            String rcptBranCode,
            String invnBranCode,
            String frngDscmCode,
            DlvrAdrsDvsnCode dlvrAdrsDvsnCode,
            String dtlAdrs1,
            String dtlAdrs2,
            String dtlAdrs3,
            String dtlAdrs4,
            String dtlAdrs5,
            String dtlAdrs6,
            String dtlAdrs7,
            String dtlAdrs8,
            String dtlAdrs9,
            String dtlAdrs10,
            String dtlAdrs11,
            String dtlAdrs12,
            String prsnEntyChaNum,
            boolean gftRecpPricExprYsno,
            boolean gddlSlctYsno,
            boolean brdrPickupboxYsno,
            String rqtrId
    ) {
        return new DeliveryInfo(
                ordrDlpnId,
                ordrId,
                mmbrNum,
                dlpnName,
                dlvrMthdDvsnCode,
                rcvrName,
                rcvrTlnm,
                rcvrPrtbTlnm,
                rcptBranCode,
                invnBranCode,
                frngDscmCode,
                dlvrAdrsDvsnCode,
                dtlAdrs1,
                dtlAdrs2,
                dtlAdrs3,
                dtlAdrs4,
                dtlAdrs5,
                dtlAdrs6,
                dtlAdrs7,
                dtlAdrs8,
                dtlAdrs9,
                dtlAdrs10,
                dtlAdrs11,
                dtlAdrs12,
                prsnEntyChaNum,
                gftRecpPricExprYsno,
                gddlSlctYsno,
                brdrPickupboxYsno,
                rqtrId,
                null,
                rqtrId,
                null,
                false,
                rqtrId
        );
    }

    public boolean isError() {
        return StringUtils.isNotBlank(this.ordrDlpnId) && this.ordrDlpnId.equals("ordrDlpnErr");
    }
}
