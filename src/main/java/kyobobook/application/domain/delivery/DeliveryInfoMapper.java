/***************************************************
 * Copyright(c) 2021-2022 Kyobo Book Centre All right reserved.
 * This software is the proprietary information of Kyobo Book.
 *
 * Revision History
 * Author                         Date          Description
 * --------------------------     ----------    ----------------------------------------
 * SeongHeon Sim     2024-04-08
 *
 ****************************************************/
package kyobobook.application.domain.delivery;

import kyobobook.application.adapter.out.persistence.entity.jpa.OrderDeliveryPointEntity;
import org.apache.commons.lang3.StringUtils;

import java.util.Objects;

/**
 * <AUTHOR> <PERSON><PERSON><PERSON><PERSON> Sim
 * @Project : fo-order-api
 * @FileName : DeliveryPointEntityMapper
 * @Date : 2024-04-08
 * @description :
 */
public class DeliveryInfoMapper {

    public static DeliveryInfo copyOf(OrderDeliveryPointEntity deliveryPoint) {

        // ordrMemoId 가 공백으로 있는 경우가 있어 해당 부분에서 제외 처리 하도록 한다.
        if (Objects.isNull(deliveryPoint) || StringUtils.isBlank(deliveryPoint.getOrdrDlpnId())) {
            return null;
        }

        return DeliveryInfo.of(
                deliveryPoint.getOrdrDlpnId(),
                deliveryPoint.getOrdrId(),
                deliveryPoint.getMmbrNum(),
                deliveryPoint.getDlpnName(),
                deliveryPoint.getDlvrMthdDvsnCode(),
                deliveryPoint.getRcvrName(),
                deliveryPoint.getRcvrTlnm(),
                deliveryPoint.getRcvrPrtbTlnm(),
                deliveryPoint.getRcptBranCode(),
                deliveryPoint.getInvnBranCode(),
                deliveryPoint.getFrngDscmCode(),
                deliveryPoint.getDlvrAdrsDvsnCode(),
                deliveryPoint.getDtlAdrs1(),
                deliveryPoint.getDtlAdrs2(),
                deliveryPoint.getDtlAdrs3(),
                deliveryPoint.getDtlAdrs4(),
                deliveryPoint.getDtlAdrs5(),
                deliveryPoint.getDtlAdrs6(),
                deliveryPoint.getDtlAdrs7(),
                deliveryPoint.getDtlAdrs8(),
                deliveryPoint.getDtlAdrs9(),
                deliveryPoint.getDtlAdrs10(),
                deliveryPoint.getDtlAdrs11(),
                deliveryPoint.getDtlAdrs12(),
                deliveryPoint.getPrsnEntyChaNum(),
                deliveryPoint.getGftRecpPricExprYsno(),
                deliveryPoint.getGddlSlctYsno(),
                deliveryPoint.getBrdrPickupboxYsno(),
                deliveryPoint.getCrtrId()
        );
    }

    public static DeliveryInfo copyOf(
            DeliveryInfo deliveryInfo,
            String rqtrId
    ) {
        return DeliveryInfo.of(
                deliveryInfo.ordrDlpnId(),
                deliveryInfo.ordrId(),
                deliveryInfo.mmbrNum(),
                deliveryInfo.dlpnName(),
                deliveryInfo.dlvrMthdDvsnCode(),
                deliveryInfo.rcvrName(),
                deliveryInfo.rcvrTlnm(),
                deliveryInfo.rcvrPrtbTlnm(),
                deliveryInfo.rcptBranCode(),
                deliveryInfo.invnBranCode(),
                deliveryInfo.frngDscmCode(),
                deliveryInfo.dlvrAdrsDvsnCode(),
                deliveryInfo.dtlAdrs1(),
                deliveryInfo.dtlAdrs2(),
                deliveryInfo.dtlAdrs3(),
                deliveryInfo.dtlAdrs4(),
                deliveryInfo.dtlAdrs5(),
                deliveryInfo.dtlAdrs6(),
                deliveryInfo.dtlAdrs7(),
                deliveryInfo.dtlAdrs8(),
                deliveryInfo.dtlAdrs9(),
                deliveryInfo.dtlAdrs10(),
                deliveryInfo.dtlAdrs11(),
                deliveryInfo.dtlAdrs12(),
                deliveryInfo.prsnEntyChaNum(),
                deliveryInfo.gftRecpPricExprYsno(),
                deliveryInfo.gddlSlctYsno(),
                deliveryInfo.brdrPickupboxYsno(),
                rqtrId
        );
    }
}
